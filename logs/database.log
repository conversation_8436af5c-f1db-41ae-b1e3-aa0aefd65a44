2025-08-22 10:49:06,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice Payments` ADD COLUMN `payment_comments` varchar(140)
2025-08-22 10:49:13,837 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-22 16:09:15,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` ADD COLUMN `purchase_order_number` varchar(140)
2025-08-22 16:10:01,908 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-22 16:14:53,849 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-22 16:18:26,812 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-22 16:18:27,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabBill Item Details` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-08-25 09:17:56,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` ADD COLUMN `test_data` varchar(140)
2025-08-25 09:17:56,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` ADD COLUMN `test_data` varchar(140)
2025-08-25 09:18:38,820 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-25 09:19:52,702 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-25 09:19:53,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` MODIFY `tax` decimal(21,9) not null default 0, MODIFY `payload` json, MODIFY `response_data` json, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0
2025-08-25 09:21:33,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` MODIFY `response_data` json, MODIFY `tax` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `payload` json, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-08-25 09:21:33,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` MODIFY `response_data` json, MODIFY `tax` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `payload` json, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-08-25 09:21:42,107 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-25 09:21:43,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` MODIFY `response_data` json, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `tax` decimal(21,9) not null default 0, MODIFY `payload` json, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-08-26 09:14:03,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` MODIFY `response_data` json, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `payload` json, MODIFY `tax` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0
2025-08-26 09:21:49,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `payload` json, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `tax` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `response_data` json
2025-08-26 11:15:32,632 WARNING database DDL Query made to DB:
alter table `tabInvoice Items` add column if not exists parent varchar(140)
2025-08-26 11:15:32,633 WARNING database DDL Query made to DB:
alter table `tabInvoice Items` add column if not exists parenttype varchar(140)
2025-08-26 11:15:32,633 WARNING database DDL Query made to DB:
alter table `tabInvoice Items` add column if not exists parentfield varchar(140)
2025-08-26 11:15:32,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice Items` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-08-26 11:15:49,782 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-26 11:15:50,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice Items` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-26 11:15:50,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `payload` json, MODIFY `response_data` json, MODIFY `tax` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-08-27 11:59:20,237 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parent varchar(140)
2025-08-27 11:59:20,245 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parenttype varchar(140)
2025-08-27 11:59:20,246 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parentfield varchar(140)
2025-08-31 17:29:28,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Instructions` ADD COLUMN `to_order_indicator` int(1) not null default 0
2025-09-01 16:55:59,147 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parent varchar(140)
2025-09-01 16:55:59,147 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parent varchar(140)
2025-09-01 16:55:59,147 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parent varchar(140)
2025-09-01 16:55:59,147 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parent varchar(140)
2025-09-01 16:55:59,147 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parent varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parenttype varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parenttype varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parenttype varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parenttype varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parenttype varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parentfield varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parentfield varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parentfield varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parentfield varchar(140)
2025-09-01 16:55:59,150 WARNING database DDL Query made to DB:
alter table `tabInvoice Update History` add column if not exists parentfield varchar(140)
2025-09-01 16:55:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice Update History` MODIFY `new_value` text, MODIFY `old_value` text
2025-09-01 16:55:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice Update History` MODIFY `new_value` text, MODIFY `old_value` text
2025-09-01 16:55:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice Update History` MODIFY `new_value` text, MODIFY `old_value` text
2025-09-01 16:55:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice Update History` MODIFY `new_value` text, MODIFY `old_value` text
2025-09-01 16:55:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice Update History` MODIFY `new_value` text, MODIFY `old_value` text
2025-09-02 10:52:11,671 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parent varchar(140)
2025-09-02 10:52:11,673 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parenttype varchar(140)
2025-09-02 10:52:11,674 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parentfield varchar(140)
2025-09-02 10:52:11,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabICS2 Parties Involved` ADD COLUMN `address` text
2025-09-02 10:52:18,594 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parent varchar(140)
2025-09-02 10:52:18,595 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parenttype varchar(140)
2025-09-02 10:52:18,596 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parentfield varchar(140)
2025-09-02 11:00:29,488 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-02 11:00:30,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabEquipments` ADD COLUMN `message_version` varchar(140), ADD COLUMN `message_date_time_inttra` datetime(6), ADD COLUMN `state` varchar(140), ADD COLUMN `inttra_status` varchar(140), ADD COLUMN `inttra_evgmid` varchar(140), ADD COLUMN `xml_file_name_inttra` varchar(140), ADD COLUMN `forwarders_reference_number` varchar(140), ADD COLUMN `inttra_error_code` varchar(140), ADD COLUMN `inttra_error_details` text, ADD COLUMN `shippers_reference_number` varchar(140), ADD COLUMN `carrier_status` varchar(140), ADD COLUMN `message_date_time_carrier` datetime(6), ADD COLUMN `xml_file_name_carrier` varchar(140), ADD COLUMN `carrier_error_code` varchar(140), ADD COLUMN `carrier_error_details` text
2025-09-02 11:00:30,868 WARNING database DDL Query made to DB:
ALTER TABLE `tabEquipments` MODIFY `response_haulage_details` json
2025-09-02 11:00:31,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabInvoice DB` MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `response_data` json, MODIFY `tax` decimal(21,9) not null default 0, MODIFY `payload` json
2025-09-02 11:00:31,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabSFTP Downloaded Files` ADD COLUMN `connected_doctype_id` varchar(140)
2025-09-02 15:00:09,932 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parent varchar(140)
2025-09-02 15:00:09,932 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parent varchar(140)
2025-09-02 15:00:09,933 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parenttype varchar(140)
2025-09-02 15:00:09,933 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parenttype varchar(140)
2025-09-02 15:00:09,933 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parentfield varchar(140)
2025-09-02 15:00:09,933 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parentfield varchar(140)
2025-09-02 15:00:10,008 WARNING database DDL Query made to DB:
ALTER TABLE `tabICS2 Parties Involved` ADD COLUMN `contact_name` varchar(140)
2025-09-02 15:00:10,008 WARNING database DDL Query made to DB:
ALTER TABLE `tabICS2 Parties Involved` ADD COLUMN `contact_name` varchar(140)
2025-09-02 15:00:25,894 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parent varchar(140)
2025-09-02 15:00:25,894 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parent varchar(140)
2025-09-02 15:00:25,894 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parenttype varchar(140)
2025-09-02 15:00:25,894 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parenttype varchar(140)
2025-09-02 15:00:25,895 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parentfield varchar(140)
2025-09-02 15:00:25,895 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parentfield varchar(140)
2025-09-02 15:00:25,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabICS2 Parties Involved` ADD COLUMN `party_id` varchar(140)
2025-09-02 15:00:25,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabICS2 Parties Involved` ADD COLUMN `party_id` varchar(140)
2025-09-02 15:24:51,151 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parent varchar(140)
2025-09-02 15:24:51,151 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parenttype varchar(140)
2025-09-02 15:24:51,151 WARNING database DDL Query made to DB:
alter table `tabICS2 Parties Involved` add column if not exists parentfield varchar(140)
2025-09-02 15:26:09,479 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-08 11:50:50,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer DB` ADD COLUMN `street_name` varchar(140), ADD COLUMN `street_no` varchar(140), ADD COLUMN `po_box` varchar(140)
2025-09-08 11:50:50,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer DB` ADD COLUMN `street_name` varchar(140), ADD COLUMN `street_no` varchar(140), ADD COLUMN `po_box` varchar(140)
2025-09-08 11:51:04,579 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-08 11:51:05,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabVendor` ADD COLUMN `secret_pin` varchar(140)
2025-09-08 11:51:05,853 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob` ADD COLUMN `carrier_booking_number` varchar(140)
2025-09-08 11:51:06,053 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocket DB` ADD COLUMN `carrier_booking_number` varchar(140)
2025-09-08 11:55:09,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer DB` ADD COLUMN `street_number` varchar(140)
