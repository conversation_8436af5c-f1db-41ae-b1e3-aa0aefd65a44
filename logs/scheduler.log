2025-07-24 17:31:10,872 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for westside.local
2025-07-24 17:31:10,877 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-24 17:31:10,879 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-07-24 17:31:10,880 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-24 17:31:10,881 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-24 17:32:10,892 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-07-24 17:32:10,895 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-24 17:32:10,898 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for westside.local
2025-07-24 17:32:10,905 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-24 17:32:10,906 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-24 17:32:10,907 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-24 17:32:10,909 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-24 17:46:11,319 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-24 17:46:11,329 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-07-24 17:47:11,362 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-24 17:47:11,371 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-07-24 18:01:11,843 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-24 18:01:11,844 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-07-24 18:01:11,845 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-07-24 18:01:11,847 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for westside.local
2025-07-24 18:01:11,847 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-24 18:01:11,849 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for westside.local
2025-07-24 18:01:11,850 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for westside.local
2025-07-24 18:01:11,851 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-24 18:01:11,851 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for westside.local
2025-07-24 18:01:11,853 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for westside.local
2025-07-24 18:01:11,855 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-24 18:01:11,857 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for westside.local
2025-07-24 18:01:11,859 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for westside.local
2025-07-24 18:02:11,865 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-24 18:02:11,873 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for westside.local
2025-07-24 18:02:11,875 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for westside.local
2025-07-24 18:02:11,877 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-24 18:02:11,879 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for westside.local
2025-07-24 18:02:11,880 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for westside.local
2025-07-24 18:02:11,881 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for westside.local
2025-07-24 18:02:11,883 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for westside.local
2025-07-24 18:02:11,885 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-07-24 18:02:11,886 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-24 18:02:11,887 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for westside.local
2025-07-24 18:02:11,890 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-07-24 18:02:11,892 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-25 11:31:01,755 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for westside.local
2025-07-25 11:31:01,763 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-25 11:31:01,770 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-25 11:31:01,771 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-25 11:31:01,772 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-07-25 11:31:01,773 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-07-25 11:32:01,781 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-07-25 11:32:01,784 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-25 11:32:01,789 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-25 11:32:01,794 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-25 11:32:01,796 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for westside.local
2025-07-25 11:32:01,797 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-07-26 15:31:14,570 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-26 15:31:14,600 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-26 15:31:14,606 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-26 15:31:14,631 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-07-31 15:01:23,449 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for westside.local
2025-07-31 15:01:23,480 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for westside.local
2025-07-31 15:01:23,493 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-07-31 15:01:23,498 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for westside.local
2025-07-31 15:16:24,641 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-31 15:16:24,652 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-31 15:16:24,674 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-31 15:16:24,685 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-07-31 15:16:24,688 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-31 15:16:24,708 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-31 15:17:24,734 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-31 15:17:24,743 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-07-31 15:17:24,753 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-07-31 15:17:24,773 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for westside.local
2025-07-31 15:17:24,778 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-31 15:17:24,785 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-31 15:17:24,792 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-31 15:17:24,817 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-31 15:18:24,841 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for westside.local
2025-07-31 15:18:24,847 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-31 15:18:24,851 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-31 15:18:24,856 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-07-31 15:18:24,861 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-31 15:18:24,874 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-31 15:18:24,878 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-31 15:18:24,885 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-07-31 15:19:24,916 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-07-31 15:19:24,921 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-07-31 15:19:24,938 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-31 15:19:24,950 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for westside.local
2025-07-31 15:19:24,955 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-31 15:19:24,962 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-07-31 15:19:24,969 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-31 15:19:24,973 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-31 15:20:24,999 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-07-31 15:20:25,004 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-07-31 15:20:25,018 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for westside.local
2025-07-31 15:20:25,024 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-07-31 15:20:25,057 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-07-31 15:20:25,063 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-07-31 15:20:25,066 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-07-31 15:20:25,070 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-08-02 12:31:54,402 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-08-02 12:31:54,435 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-08-02 12:31:54,446 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-08-02 12:31:54,458 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-08-06 21:46:36,426 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-08-06 21:46:36,444 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-08-06 21:46:36,458 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for westside.local
2025-08-06 21:46:36,460 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-08-06 21:46:36,464 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-08-06 21:46:36,467 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-08-06 21:46:36,470 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-08-07 10:31:09,717 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-08-07 10:31:09,729 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-08-07 10:31:09,733 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-08-07 10:31:09,741 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-08-07 10:32:09,780 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-08-07 10:32:09,809 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-08-07 10:32:09,813 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-08-07 10:32:09,833 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-08-07 10:33:09,851 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-08-07 10:33:09,867 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-08-07 10:33:09,873 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-08-07 10:33:09,882 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-08-07 10:33:09,891 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-08-07 10:33:09,897 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for westside.local
2025-08-07 10:34:09,943 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-08-07 10:34:09,951 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-08-07 10:34:09,954 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-08-07 10:34:09,972 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-08-07 10:34:09,975 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for westside.local
2025-08-07 10:34:09,977 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-08-07 10:35:10,006 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-08-07 10:35:10,028 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for westside.local
2025-08-07 10:35:10,038 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-08-07 10:35:10,045 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for westside.local
2025-08-07 10:35:10,059 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-08-07 10:35:10,070 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-08-25 08:51:13,636 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for westside.local
2025-08-25 08:51:13,639 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for westside.local
2025-08-25 08:51:13,643 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for westside.local
2025-08-25 08:51:13,645 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-08-25 08:51:13,648 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for westside.local
2025-08-25 08:51:13,650 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-08-25 08:51:13,652 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for westside.local
2025-08-25 08:51:13,653 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for westside.local
2025-08-25 08:51:13,655 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-08-25 08:51:13,658 ERROR scheduler Skipped queueing westside.api.email_schedule.email_cron_job because it was found in queue for westside.local
2025-08-25 08:51:13,662 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for westside.local
2025-08-25 08:51:13,667 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for westside.local
2025-08-25 08:51:13,673 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for westside.local
2025-08-25 08:51:13,680 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for westside.local
2025-08-25 08:51:13,682 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for westside.local
2025-08-25 08:51:13,686 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for westside.local
2025-08-25 08:51:13,688 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for westside.local
2025-08-25 08:51:13,690 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for westside.local
2025-08-25 08:51:13,693 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for westside.local
2025-08-25 08:51:13,696 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for westside.local
2025-08-25 08:51:13,699 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for westside.local
2025-08-25 08:51:13,701 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for westside.local
2025-08-25 08:51:13,705 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for westside.local
2025-08-25 08:51:13,710 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for westside.local
2025-08-25 08:51:13,713 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for westside.local
2025-08-28 18:12:51,144 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for westside.local
2025-09-01 18:31:16,497 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-09-02 15:01:14,177 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-09-02 15:02:14,227 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for westside.local
2025-09-03 14:00:53,122 ERROR scheduler Exception in Enqueue Events for Site westside.local
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 649, in connect
    sock = socket.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/socket.py", line 852, in create_connection
    raise exceptions[0]
  File "/usr/lib/python3.12/socket.py", line 837, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 99, in enqueue_events_for_site
    if is_scheduler_inactive():
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 143, in is_scheduler_inactive
    if is_scheduler_disabled(verbose=verbose):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/scheduler.py", line 156, in is_scheduler_disabled
    frappe.db.get_single_value("System Settings", "enable_scheduler")
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 837, in get_single_value
    ).run()
      ^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
                                                             ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
  File "/home/<USER>/westside-bench/env/lib/python3.12/site-packages/pymysql/connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '127.0.0.1' ([Errno 111] Connection refused)")
2025-09-08 16:31:39,823 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-09-08 16:31:39,835 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-09-08 16:31:39,838 ERROR scheduler Skipped queueing westside.api.email_schedule.email_cron_job because it was found in queue for westside.local
2025-09-08 16:31:39,858 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-09-08 16:31:39,859 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-09-08 16:32:39,883 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for westside.local
2025-09-08 16:32:39,897 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for westside.local
2025-09-08 16:32:39,911 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for westside.local
2025-09-08 16:32:39,924 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for westside.local
2025-09-08 16:32:39,936 ERROR scheduler Skipped queueing westside.api.email_schedule.email_cron_job because it was found in queue for westside.local
