<?xml version='1.0' encoding='utf-8'?>
<Message>
  <Header>
    <MessageType MessageVersion="1.0">ShippingInstruction</MessageType>
    <DocumentIdentifier>SI-0098</DocumentIdentifier>
    <DateTime DateType="Document">2508141024</DateTime>
    <Parties>
      <PartnerInformation PartnerRole="Sender">
        <PartnerIdentifier Agency="AssignedBySender">WESELL</PartnerIdentifier>
        <PartnerName>Westside Exports LLC</PartnerName>
        <ContactInformation>
          <ContactName ContactType="Informational">Westside Exports LLC</ContactName>
          <CommunicationValue CommunicationType="Telephone">************</CommunicationValue>
          <CommunicationValue CommunicationType="Email"><EMAIL></CommunicationValue>
        </ContactInformation>
      </PartnerInformation>
      <PartnerInformation PartnerRole="Recipient">
        <PartnerIdentifier Agency="AssignedByRecipient">INTTRA</PartnerIdentifier>
      </PartnerInformation>
    </Parties>
  </Header>
  <MessageBody>
    <MessageProperties>
      <ShipmentID>
        <ShipmentIdentifier MessageStatus="Original">SI-0098</ShipmentIdentifier>
        <DocumentVersion>1.0</DocumentVersion>
      </ShipmentID>
      <DateTime DateType="Message">202508141024</DateTime>
      <ChargeCategory PrepaidorCollectIndicator="Prepaid" ChargeType="BasicFreight"/>
      <ChargeCategory PrepaidorCollectIndicator="Prepaid" ChargeType="OriginPortCharges"/>
      <ShipmentDeclaredValue Currency="USD">8500</ShipmentDeclaredValue>
      <ReferenceInformation ReferenceType="BookingNumber">247002024</ReferenceInformation>
      <ReferenceInformation ReferenceType="ShipperIdentifyingNumber">AAICG8102C</ReferenceInformation>
      <ReferenceInformation ReferenceType="BillOfLadingNumber">BL12345678</ReferenceInformation>
      <ReferenceInformation ReferenceType="TransactionReferenceNumber">Tnx14578963</ReferenceInformation>
      <ReferenceInformation ReferenceType="PurchaseOrderNumber">123456PON</ReferenceInformation>
      <ReferenceInformation ReferenceType="ContractNumber">123456CN</ReferenceInformation>
      <ReferenceInformation ReferenceType="SupplementaryDeclarantEORI">US12345678901234</ReferenceInformation>
      <ReferenceInformation ReferenceType="HouseBillNumber">HBL123456789</ReferenceInformation>
      <ReferenceInformation ReferenceType="PCINNumber">PCIN12345678</ReferenceInformation>
      <ReferenceInformation ReferenceType="CSNNumber">CSN7896654123</ReferenceInformation>
      <ReferenceInformation ReferenceType="MCINNumber">MCIN7896554123</ReferenceInformation>
      <Instructions>
        <ShipmentComments CommentType="General">Test Comments</ShipmentComments>
      </Instructions>
      <ControlTotal>
        <NumberOfEquipment>3</NumberOfEquipment>
        <NumberOfPackages>30</NumberOfPackages>
        <GrossWeight UOM="KGM">75000</GrossWeight>
        <GrossVolume UOM="MTQ">225</GrossVolume>
      </ControlTotal>
      <HaulageDetails MovementType="PortToPort" ServiceType="FullLoad"/>
      <TransportationDetails TransportStage="Main" TransportMode="Maritime">
        <ConveyanceInformation>
          <ConveyanceName>MAERSK NACALA</ConveyanceName>
          <VoyageTripNumber>534W</VoyageTripNumber>
          <CarrierSCAC>MAEU</CarrierSCAC>
        </ConveyanceInformation>
        <Location LocationType="PlaceOfReceipt">
          <LocationCode Agency="UN">PRSJU</LocationCode>
          <LocationName>SAN JUAN</LocationName>
          <LocationCountry>PR</LocationCountry>
        </Location>
        <Location LocationType="PortOfLoading">
          <LocationCode Agency="UN">PRSJU</LocationCode>
          <LocationName>SAN JUAN</LocationName>
          <LocationCountry>PR</LocationCountry>
        </Location>
        <Location LocationType="PortOfDischarge">
          <LocationCode Agency="UN">INNSA</LocationCode>
          <LocationName>NHAVA SHEVA (JAWAHARLAL NEHRU)</LocationName>
          <LocationCountry>IN</LocationCountry>
        </Location>
        <Location LocationType="BillOfLadingOrigin">
          <LocationCode Agency="UN">NFNLK</LocationCode>
          <LocationName>NORFOLK ISLAND</LocationName>
          <LocationCountry>NF</LocationCountry>
        </Location>
        <Location LocationType="PlaceOfFinalDelivery">
          <LocationCode Agency="UN">INNSA</LocationCode>
          <LocationName>NHAVA SHEVA (JAWAHARLAL NEHRU)</LocationName>
          <LocationCountry>IN</LocationCountry>
        </Location>
      </TransportationDetails>
      <Parties>
        <PartnerInformation PartnerRole="BLNotifyParty">
          <ContactInformation>
            <ContactName ContactType="BLAutoShare">ROHN</ContactName>
            <WebBLDocuments BLDocType="DraftUnrated"/>
            <CommunicationValue CommunicationType="Email"><EMAIL></CommunicationValue>
          </ContactInformation>
        </PartnerInformation>
        <PartnerInformation PartnerRole="Shipper">
          <PartnerIdentifier Agency="AssignedBySender">WESELL</PartnerIdentifier>
          <PartnerName>westside exports llc</PartnerName>
          <ContactInformation>
            <ContactName ContactType="Informational">Janardhan Uppalapatti</ContactName>
            <CommunicationValue CommunicationType="Email"><EMAIL></CommunicationValue>
            <CommunicationValue CommunicationType="Telephone">************</CommunicationValue>
          </ContactInformation>
          <AddressInformation>
            <AddressLine>4017 vallonia dr CARY NORTH CAROLIN</AddressLine>
            <AddressLine>A 27519 UNITED STATES</AddressLine>
          </AddressInformation>
          <PartyReferenceInformation ReferenceType="GovtReferenceNumber">AAICG8102C</PartyReferenceInformation>
        </PartnerInformation>
        <PartnerInformation PartnerRole="Consignee">
          <PartnerName>FINSTER BLACK PRIVATE LIMITED</PartnerName>
          <ContactInformation>
            <ContactName ContactType="Informational">FINSTER BLACK PRIVATE LIMITED</ContactName>
            <CommunicationValue CommunicationType="Email"><EMAIL></CommunicationValue>
            <CommunicationValue CommunicationType="Telephone">9878987878</CommunicationValue>
          </ContactInformation>
          <AddressInformation>
            <AddressLine>SR#28/2,BLD.#6,7,9,28,29,30,&amp; 3</AddressLine>
            <AddressLine>1,VILLAGE VANIVALI,KHALAPUR,RAIGAD</AddressLine>
          </AddressInformation>
          <PartyReferenceInformation ReferenceType="GovtReferenceNumber">AADCF8884D</PartyReferenceInformation>
        </PartnerInformation>
        <PartnerInformation PartnerRole="NotifyParty">
          <PartnerName>test customer</PartnerName>
          <ContactInformation>
            <ContactName ContactType="Informational">FINSTER BLACK PRIVATE LIMITED</ContactName>
            <CommunicationValue CommunicationType="Email"><EMAIL></CommunicationValue>
            <CommunicationValue CommunicationType="Telephone">9876543210</CommunicationValue>
          </ContactInformation>
          <AddressInformation>
            <AddressLine>SR#28/2,BLD.#6,7,9,28,29,30&amp;31,</AddressLine>
            <AddressLine>VILL AGE VANIVALI,KHALAPUR,RAIGAD</AddressLine>
          </AddressInformation>
          <PartyReferenceInformation ReferenceType="GovtReferenceNumber">AAICG8102C</PartyReferenceInformation>
        </PartnerInformation>
        <PartnerInformation PartnerRole="Requestor">
          <PartnerIdentifier Agency="AssignedBySender">WESELL</PartnerIdentifier>
          <PartnerName>Westside Exports LLC</PartnerName>
          <ContactInformation>
            <ContactName ContactType="Informational">Westside Exports LLC</ContactName>
            <CommunicationValue CommunicationType="Telephone">************</CommunicationValue>
            <CommunicationValue CommunicationType="Email"><EMAIL></CommunicationValue>
          </ContactInformation>
          <DocumentationRequirements>
            <Documents DocumentType="BillOfLadingOriginal" Freighted="False"/>
            <Quantity>2</Quantity>
          </DocumentationRequirements>
        </PartnerInformation>
        <PartnerInformation PartnerRole="MessageRecipient">
          <ContactInformation>
            <ContactName ContactType="SINotification">ROHN</ContactName>
            <CommunicationValue CommunicationType="Email"><EMAIL></CommunicationValue>
          </ContactInformation>
        </PartnerInformation>
        <PartnerInformation PartnerRole="Exporter">
          <PartnerName>Westside</PartnerName>
          <ContactInformation>
            <ContactName ContactType="Informational">John Doe</ContactName>
            <CommunicationValue CommunicationType="Telephone">123456789</CommunicationValue>
          </ContactInformation>
          <AddressInformation>
            <AddressLine>ABC Street 35</AddressLine>
            <City>Test</City>
            <StateProvince>State</StateProvince>
            <PostalCode>685556</PostalCode>
            <CountryCode>IN</CountryCode>
            <Street>ABC</Street>
            <StreetNumber>35</StreetNumber>
            <PoBox>689532</PoBox>
          </AddressInformation>
          <PartyReferenceInformation ReferenceType="GovtReferenceNumber">123456789</PartyReferenceInformation>
        </PartnerInformation>
        <PartnerInformation PartnerRole="UltimateConsignee">
          <PartnerName>Westside Exports</PartnerName>
          <ContactInformation>
            <ContactName ContactType="Informational">John Doe</ContactName>
            <CommunicationValue CommunicationType="Telephone">123456789</CommunicationValue>
          </ContactInformation>
          <AddressInformation>
            <AddressLine>Vallonia Dr</AddressLine>
            <City>City Test</City>
            <StateProvince>Test State</StateProvince>
            <PostalCode>457893</PostalCode>
            <CountryCode>US</CountryCode>
            <Street>Test</Street>
            <StreetNumber>35</StreetNumber>
            <PoBox>356892</PoBox>
          </AddressInformation>
          <PartyReferenceInformation ReferenceType="GovtReferenceNumber">789665541</PartyReferenceInformation>
        </PartnerInformation>
        <PartnerInformation PartnerRole="Carrier">
          <PartnerIdentifier Agency="AssignedBySender">MAEU</PartnerIdentifier>
          <PartnerName>MAERSK</PartnerName>
          <AddressInformation>
            <AddressLine>180 Park Avenue,Building 105,PO Box</AddressLine>
            <AddressLine> 950,07932 Florham Park,NJ,USA.</AddressLine>
            <PostalCode>07932</PostalCode>
            <CountryCode>US</CountryCode>
          </AddressInformation>
        </PartnerInformation>
      </Parties>
      <Ics2EnsFiler>Carrier</Ics2EnsFiler>
      <GoodsDeliveredInEu>No</GoodsDeliveredInEu>
      <HouseBillIndicator>HBL</HouseBillIndicator>
      <MethodOfPayment>AccountHolderWithCarrier</MethodOfPayment>
      <CountriesOfRoutingOfConsignment>CA-US-HK</CountriesOfRoutingOfConsignment>
    </MessageProperties>
    <MessageDetails>
      <EquipmentDetails>
        <LineNumber>1</LineNumber>
        <EquipmentIdentifier EquipmentSupplier="Carrier">CONT0002579</EquipmentIdentifier>
        <EquipmentType>
          <EquipmentTypeCode>20GP</EquipmentTypeCode>
          <EquipmentDescription>20 General Purpose (20GP)</EquipmentDescription>
        </EquipmentType>
        <EquipmentSeal SealingParty="Shipper">Seal123</EquipmentSeal>
      </EquipmentDetails>
      <EquipmentDetails>
        <LineNumber>2</LineNumber>
        <EquipmentIdentifier EquipmentSupplier="Carrier">CONT0002575</EquipmentIdentifier>
        <EquipmentType>
          <EquipmentTypeCode>20GP</EquipmentTypeCode>
          <EquipmentDescription>20 General Purpose (20GP)</EquipmentDescription>
        </EquipmentType>
        <EquipmentSeal SealingParty="Shipper">Seal123</EquipmentSeal>
      </EquipmentDetails>
      <EquipmentDetails>
        <LineNumber>3</LineNumber>
        <EquipmentIdentifier EquipmentSupplier="Carrier">FAIR123456</EquipmentIdentifier>
        <EquipmentType>
          <EquipmentTypeCode>25G0</EquipmentTypeCode>
          <EquipmentDescription>20 High Cube (25G0)</EquipmentDescription>
        </EquipmentType>
        <EquipmentSeal SealingParty="Shipper">S123222</EquipmentSeal>
      </EquipmentDetails>
      <GoodsDetails>
        <LineNumber>1</LineNumber>
        <PackageDetail Level="outer">
          <NumberOfPackages>30</NumberOfPackages>
          <PackageTypeCode>BL</PackageTypeCode>
          <PackageTypeDescription>Bale, compressed</PackageTypeDescription>
        </PackageDetail>
        <PackageDetailComments CommentType="GoodsDescription">Waste, parings and scrap of rubber</PackageDetailComments>
        <PackageDetailComments CommentType="GoodsDescription">(other than hard rubber) and</PackageDetailComments>
        <PackageDetailComments CommentType="GoodsDescription">powders and granules obtained</PackageDetailComments>
        <PackageDetailComments CommentType="GoodsDescription">therefrom</PackageDetailComments>
        <ProductId ItemTypeIdCode="HarmonizedSystem">400400</ProductId>
        <PackageDetailGrossVolume UOM="MTQ">225</PackageDetailGrossVolume>
        <PackageDetailGrossWeight UOM="KGM">75000</PackageDetailGrossWeight>
        <SplitGoodsDetails>
          <EquipmentIdentifier>CONT0002579</EquipmentIdentifier>
          <SplitGoodsNumberOfPackages>10</SplitGoodsNumberOfPackages>
          <SplitGoodsGrossVolume UOM="MTQ">75.0</SplitGoodsGrossVolume>
          <SplitGoodsGrossWeight UOM="KGM">25000</SplitGoodsGrossWeight>
        </SplitGoodsDetails>
        <SplitGoodsDetails>
          <EquipmentIdentifier>CONT0002575</EquipmentIdentifier>
          <SplitGoodsNumberOfPackages>10</SplitGoodsNumberOfPackages>
          <SplitGoodsGrossVolume UOM="MTQ">75.0</SplitGoodsGrossVolume>
          <SplitGoodsGrossWeight UOM="KGM">25000</SplitGoodsGrossWeight>
        </SplitGoodsDetails>
        <SplitGoodsDetails>
          <EquipmentIdentifier>FAIR123456</EquipmentIdentifier>
          <SplitGoodsNumberOfPackages>10</SplitGoodsNumberOfPackages>
          <SplitGoodsGrossVolume UOM="MTQ">75.0</SplitGoodsGrossVolume>
          <SplitGoodsGrossWeight UOM="KGM">25000</SplitGoodsGrossWeight>
        </SplitGoodsDetails>
      </GoodsDetails>
    </MessageDetails>
  </MessageBody>
</Message>
