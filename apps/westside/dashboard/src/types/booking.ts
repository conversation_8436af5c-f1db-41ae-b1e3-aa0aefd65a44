export enum BookingStatusEnums {
  REQUEST = "REQUEST",
  CONFIRM = "CONFIRM",
  CANCEL = "CANCEL",
  AMEND = "AMEND",
  DECLINE = "DECLINE",
  REPLACE = "REPLACE",
  FAILED = "FAILED",
}

export type LocationType = {
  name: string;
  location_name: string;
  country: string;
  country_code: string;
  sub_division: string | null;
  locode: string;
};

export type MyBookingType = {
  booking_id: string;
  booking_date: string;
  party_code: string;
  party_name: string;
  main_carriage: {
    voyage: null | string;
    vessel: null | string;
    eta: string;
    etd: string;
    port_of_discharge: string;
    port_of_load: string;
  }[];
  containers: {
    container_quantitytype: string | null;
    container_descp: string | null;
    container_type_code: null | string;
  }[];
  cargo: {
    cargo_description: string;
  }[];
  location_name: string;
  place_of_carrier_receipt: {
    location_name: string;
    date: string;
  };
  place_of_carrier_delivery: {
    location_name: string;
    date: string;
  };
  party_short_name: string;
  departure_date: string;
  inttra_reference: string;
  last_modified: string;
  booking_status: BookingStatusEnums;
  carrier_booking_number?: string;
  carrier_source_booking_number?: string;
  si_due_date: string | null;
  vgm_due_date: string | null;
  cy_date: string | null;
  doc_cut_of_date?: string;
  total_containers: number;
  dat_departure: string | null;
  dat_arrival: string | null;
  vessel_and_voyage: string | null;
  out_side_booking: number;
  si_id: string;
  si_carrier_status: string | null;
};

export type MyBookingResponseType = {
  total_count: 25;
  page: 1;
  page_size: 10;
  total_pages: 3;
  bookings: MyBookingType[];
};

// Booking Request
export type BookingRequestGeneralType = {
  data: {
    carriers: BookingCareersGeneralType[];
    container_types: BookingContainerTypeGeneralType[];
    locations: BookingLocationGeneralType[];
    hs_codes: BookingHSCodeGeneralType[];
    shippers: BookingShipperGeneralType[];
    customers: BookingCustomersGeneralType[];
    contract_party: BookingContractPartyGeneralType[];
    notify_party: BookingNotifyPartyGeneralType[];
    additional_notify_party_1: BookingNotifyParty1GeneralType[];
    additional_notify_party_2: BookingNotifyParty2GeneralType[];
  };
};

export type BookingNotifyParty1GeneralType = {
  address: string | null;
  country: string | null;
  email: string | null;
  fax: string | null;
  name: string;
  name1: string;
  phone: string | null;
  postal_code: string | null;
  my_role: number;
};
export type BookingNotifyParty2GeneralType = {
  address: string | null;
  country: string | null;
  email: string | null;
  fax: string | null;
  name: string;
  name1: string;
  phone: string | null;
  postal_code: string | null;
  my_role: number;
};
export type BookingNotifyPartyGeneralType = {
  address: string | null;
  country: string | null;
  email: string | null;
  fax: string | null;
  name: string;
  name1: string;
  phone: string | null;
  postal_code: string | null;
  my_role: number;
};

export type BookingContractPartyGeneralType = {
  address: string;
  country: string;
  email: string;
  fax: string;
  inttra_company_id: string;
  name1: string;
  name: string;
  phone: string;
  postal_code: string;
  my_role: number;
};
export type BookingCareersGeneralType = {
  name: string;
  partyalias: string;
  partyname1: string;
};

export type BookingCustomersGeneralType = {
  name: string;
  customer_name: string;
  customer_city: string;
  customer_country: string;
  customer_free_form_address: string;
  customer_state: string;
  customer_zip: string;
  email_id: string;
  fax: null | string;
  first_name: string;
  inttra_company_id: null | string;
  last_name: string;
  parent_company: string;
  phone: string;
  customer_address: string | null;
  my_role: number;
  street_number: string | null;
  street_name: string | null;
  po_box: string | null;
};

export type BookingContainerTypeGeneralType = {
  name: number;
  shortdescription: string;
  typecode: string;
};

export type BookingLocationGeneralType = {
  country: string;
  location_name: string;
  locode: string;
  name: string;
};

export type BookingHSCodeGeneralType = {
  hs_code: string;
  hs_code_description: string;
  name: string;
};

export type BookingShipperGeneralType = {
  contact_name: null | string;
  custom_address: null | string;
  email: null | string;
  inttra_company_id: null | string;
  name: string;
  phone: null | string;
  postal_code: null | string;
  shipper_code: null | string;
  shipper_details: string;
  shipper_name: string;
  subscription: null | string;
  my_role: number;
};

export type BookingOceanSheduleDataTye = {
  allowsBreakbulk: boolean;
  allowsRORO: boolean;
  bkCutoff: null | string;
  carrierName: string;
  destinationArrivalDate: string;
  destinationCityName: string;
  destinationCountry: string;
  destinationSubdivision: string;
  destinationTerminal: null | string;
  destinationUnloc: string;
  estimatedTerminalCutoff: null | string;
  hazBkCutoff: string;
  originCityName: string;
  originCountry: string;
  originDepartureDate: string;
  originSubdivision: string;
  originTerminal: null | string;
  originUnloc: string;
  reeferCutoff: string;
  scac: string;
  scheduleType: string;
  serviceName: string;
  siCutoff: string;
  terminalCutoff: string;
  totalDuration: number;
  vesselName: string;
  vgmCutoff: string;
  voyageNumber: string;
};

export type BookingConfirmationType = {
  name: string;
  modified_date: string;
  booking_agent: string;
  shipper: ShipperType[];
  booking_status: string;
  earliest_departure_date: string;
  place_of_carrier_receipt: string;
  place_of_carrier_delivery: string;
  carrier_booking_number: string;
  consignee: ConsigneeType[];
  doc_cut_of_date: string;
  port_cut_of_date: string;
  barge_cut_of_date: string;
  forwarder: string;
  contract_party: BookingContractPartyGeneralType[];
  contract_number?: string;
  shippers_reference_numbers: string;
  forwarders_reference_numbers: string;
  purchase_order_numbers: string;
  contract_party_reference_numbers: string;
  consignees_reference_numbers: string;
  bl_reference_numbers: string;
  customer_shipment_id: string;
  move_type: string;
  latest_delivery_date: string;
  number_of_containers: string;
  customer_comments: string;
  partner_email_notifications: string;
  notify_me_regarding_the_status_and_update_of_this_booking: number;
  inttra_response_status: string;
  inttra_booking_id: string;
  inttra_reference: string;
  response: any;
  si_due_date: string;
  vgm_due_date: string;
  product_channel: string;
  booking_carriages: BookingCarriage[];
  containers: Container[];
  cargo: Cargo[];
  Booking_Container: BookingContainer[];
  main_carriage: MainCarriage[];
  notify_parties: NotifyParty[];
  payment: Payment[];
  carrier: CarrierType[];
  booking_office: string;
  pre_carriage: OnCarriage[];
  on_carriage: OnCarriage[];
  haulage_arrangement: HaulageArrangement;
  first_foreign_port_of_acceptance?: string | null;
  first_us_port?: string | null;
  last_non_us_port?: string | null;
  estimated_arrival_date?: string | null;
  general_comments?: string | null;
};

type Location = {
  locode: string;
  country: string;
  country_code: string;
  location_name: string;
};

type OnCarriage = {
  start: Location;
  end: Location;
  etd: string; // or Date if you parse it
  eta: string; // or Date
  mode: string;
  mean?: string;
  carrier_code: string | null;
};
interface HaulageArrangement {
  arrangement: string;
  description: string;
}

interface BookingCarriage {
  start: string;
  start_country_code: string;
  start_location_name: string;
  etd: string;
  end: string;
  end_country_code: string;
  end_location_name: string;
  eta: string;
  mode: string;
  carrier_code?: string;
}
interface Container {
  container_type_details: {
    name: number;
    typecode: string;
    groupcode: string;
    typecategory: string;
    description: string;
    shortdescription: string;
    listheight: string;
    height: string;
    listlength: string;
    length: string;
    listwidth: string;
    width: string;
    spareindicator: number;
    nonstandardtype: number;
    displayflag: number;
    displaycategory: number;
  };
}
interface Point {
  haulageParty: HaulageParty;
  dates?: {
    dateValue: string;
    dateFormat: string;
    haulageDateType: string;
  }[];
  // You can add other fields in the Point object if present
}
interface HaulageParty {
  partyINTTRACompanyId: string | null;
  passThroughCode: string;
  partyAlias: string | null;
  dunsNumber: string | null;
  partyName1: string;
  partyRole: string;
  address: {
    unstructuredAddress01: string;
    unstructuredAddress02: string;
    unstructuredAddress03: string;
    unstructuredAddress04: string;
    street01: string;
    street02: string;
    postalCode: string;
    country: {
      countryCodeType: string;
      countryCodeValue: string;
    };
  };
}
interface ShipperType {
  contact_name?: string;
  inttra_company_id: string;
  postal_code: string;
  shipper_address: string | null;
  shipper_code: string;
  shipper_details: string;
  shipper_email: string;
  shipper_name: string;
  shipper_phone: string;
  subscription: string | null;
}

interface CarrierType {
  address: string;
  carrier_code: string;
  country_code: string;
  inttra_id: string;
  postal_code: string;
  carrier_sac: string;
  carrier_name: string;
}
interface Cargo {
  description: string;
  location: string;
  net_weight: number;
  gross_volume: number;
  net_weight_unit: string;
  cargo_gross_weight: string;
  hs_code_details: {
    hs_code: string;
    chapter_code: string;
    chapter_description: string;
    sub_chapter_code: string;
    sub_chapter_description: string;
  };
}

interface ConsigneeType {
  address: string;
  company_pass_through_code: string;
  postal_code: string;
  consigne_name?: string;
  name?: string;
}
interface BookingContainer {
  container_quantitytype: string;
  container_descp: string;
  container_name: string;
  number_of_containers: string;
  container_comments: string;
  shipper_owned: number;
  company_name: string;
  address: string;
  requested_empty_pick_up_date: string;
  time: string;
  contact_name: string;
  contact_phone_number: string;
  service_type?: string;
  weight_value?: string;
  weight_type?: string;
  weight_volume?: string;
  haulage_detail: {
    arrangement?: string;
    points?: Point[];
  };
  typecode?: string | null;
}
type ShipFrom = {
  companyName: string;
  address: string;
  emptyPositioningDate: string; // Format: YYYY-MM-DD
  emptyPositioningTime: string; // Format: HH:mm
  fullPickupDate: string; // Format: YYYY-MM-DD
  fullPickupTime: string; // Format: HH:mm
  contactName: string;
  contactNumber: string;
};
interface MainCarriage {
  voyage_number: string;
  vessel_name: string;
  eta: string;
  etd: string;
  country: string;
  lloyds_code: string;
  port_of_load: PortDetails;
  port_of_discharge: PortDetails;
  mode?: string;
  carrier_code?: string;
  mean?: string;
}

interface PortDetails {
  locode: string;
  country: string;
  country_code: string;
  location_name: string;
}

interface NotifyParty {
  name: string;
  contact: string;
  address: string;
  fax: string;
  email: string;
  postal_code: string;
  country: string;
}

interface Payment {
  charge_type: string;
  payment_term: string;
  payer: string;
  payment_location_name: string;
}

export type BookingTemplateType = {
  name: string;
  template_name: string;
  creation: string;
  modified: string;
  booking_data: string;
};

export type BookingDetailsType = {
  booking_id: string;
  port_of_origin: string;
  port_cut_of_date: string;
  doc_cut_of_date: string;
  barge_cut_of_date: string;
  inttra_reference: string;
  carrier_booking_number: string;
  commodity_name: string;
  commodity_description: string;
  commodity_hs_code: string;
};

export type EquipmentType = {
  [key: string]: any;
};

export type ContainerDataType = {
  name: string;
  assigned_equipments: EquipmentType[];
  equipments: EquipmentType[];
};

type BookingAmendPreAndOnCarriageType = {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  start: LocationType;
  etd: string;
  end: LocationType;
  eta: string;
  mode: string;
  parent: string;
  parentfield: string;
  parenttype: string;
  doctype: string;
};

export type BookingAmendBookingDataType = {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  booking_agent: {
    name: string;
    owner: string;
    creation: string;
    modified: string;
    modified_by: string;
    partyalias: string;
    partyname1: string;
    address: string;
    inttra_id: string;
    postal_code: string;
    country_code: string;
    doctype: string;
  };
  contract_number: string;
  booking_office: LocationType | string;
  custom_carrier_name: string;
  carrier_booking_number: string;
  carrier_does_not_file: number;
  filer_idscac: string;
  distinct_release_number_per_container: number;
  port_of_origin: null | string;
  doc_cut_of_date: null | string;
  port_cut_of_date: null | string;
  barge_cut_of_date: null | string;
  shipper: {
    contact_name: string;
    country: string;
    custom_address: string;
    email: string;
    name: string;
    phone: string;
    postal_code: string;
    shipper_code: string;
    shipper_details: string;
    shipper_name: string;
    street_address: string;
  };
  custom_shipper_name: string;
  consignee: {
    company_name: string;
    contact: string;
    customer_address: string;
    customer_city: string;
    customer_country: string;
    customer_name: string;
    customer_state: string;
    customer_zip: string;
    email_id: string;
    fax: string;
    name: string;
  };
  forwarder: string;
  contract_party: {
    company_name: string;
    contact: string;
    customer_address: string;
    customer_city: string;
    customer_country: string;
    customer_name: string;
    customer_state: string;
    customer_zip: string;
    doctype: string;
    email_id: string;
    fax: string;
    name: string;
  };
  notify_parties: {
    company_name: null | string;
    contact: string;
    customer_address: null | string;
    customer_city: string;
    customer_country: string;
    customer_free_form_address: string;
    customer_name: string;
    customer_state: string;
    customer_zip: string;
    name: string;
  };
  notify_party_1: {
    company_name: null | string;
    contact: string;
    customer_address: null | string;
    customer_city: string;
    customer_country: string;
    customer_free_form_address: string;
    customer_name: string;
    customer_state: string;
    customer_zip: string;
    name: string;
  };
  notify_party_2: {
    company_name: null | string;
    contact: string;
    customer_address: null | string;
    customer_city: string;
    customer_country: string;
    customer_free_form_address: string;
    customer_name: string;
    customer_state: string;
    customer_zip: string;
    name: string;
  };
  shippers_reference_numbers: string;
  forwarders_reference_numbers: string;
  purchase_order_numbers: string;
  tariff_number: string;
  contract_party_reference_numbers: string;
  consignees_reference_numbers: string;
  bl_reference_numbers: string;
  customer_shipment_id: string;
  move_type: string;
  place_of_carrier_receipt: LocationType;
  custom_place_of_carrier_receipt_name: string;
  earliest_departure_date: string;
  place_of_carrier_delivery: LocationType;
  custom_place_of_carrier_delivery_name: string;
  latest_delivery_date: string;
  customer_comments: string;
  partner_email_notifications: string;
  notify_me_regarding_the_status_and_update_of_this_booking: number;
  inttra_response_status: string;
  inttra_booking_id: string;
  inttra_reference: string;
  booking_status: string;
  si_due_date: null | string;
  vgm_due_date: null | string;
  product_channel: null | string;
  response: null | string;
  doctype: string;
  payment: {
    name: string;
    owner: string;
    creation: string;
    modified: string;
    modified_by: string;
    charge_type: string;
    payment_term: string;
    payer: string;
    payment_location: LocationType | string;
    parent: string;
    parentfield: string;
    parenttype: string;
    doctype: string;
  }[];
  cargo: {
    name: string;
    owner: string;
    creation: string;
    modified: string;
    modified_by: string;
    container_number: string | null;
    package_counttype_outermost: string | null;
    package_count: string | null;
    hs_code: string;
    origin_of_goods: string | null;
    cargo_description: string;
    schedule_b_number: string | null;
    cus_code: string | null;
    cargo_gross_weight: string;
    net_weight: number;
    net_weight_unit: string;
    gross_volume: number;
    gross_volume_unit: string;
    print_on_bl_as: string | null;
    ncm_codes: string | null;
    marks_and_numbers: string | null;
    parent: string;
    parentfield: string;
    parenttype: string;
    doctype: string;
  }[];
  main_carriage: {
    name: string;
    owner: string;
    creation: string;
    modified: string;
    modified_by: string;
    port_of_load: LocationType;
    etd: string;
    vessel: string;
    carrier: string | null;
    lloyds_code: string | null;
    port_of_discharge: LocationType;
    eta: string;
    voyage: string;
    country: string | null;
    parent: string;
    parentfield: string;
    parenttype: string;
    doctype: string;
  }[];
  add_pre_carriage: BookingAmendPreAndOnCarriageType[];
  containers: {
    name: string;
    owner: string;
    creation: string;
    modified: string;
    modified_by: string;
    number_of_containers: string;
    container_quantitytype: string;
    container_descp: string;
    container_name: string | null;
    container_comments: string;
    shipper_owned: number;
    haulage_detail: string;
    parent: string;
    parentfield: string;
    parenttype: string;
    doctype: string;
  }[];
  add_on_carriage: BookingAmendPreAndOnCarriageType[];
};
export type TrackAndTraceItemType = {
  billOfLadingNumber: string | null;
  carrierBookingReference: string | null;
  carrierScac: string;
  equipmentReference: string;
  equipmentSizeTypeCode: string;
  equipmentSizeTypeDescription: string;
  events: {
    carrierScac: string;
    carrierBookingReference: string | null;
    billOfLadingNumber: string | null;
    comments: string | null;
    emptyIndicatorCode: string;
    emptyIndicatorCodeDescription: string;
    equipmentReference: string;
    equipmentSizeTypeCode: string;
    equipmentSizeTypeDescription: string;
    eventClassifierCode: string;
    eventDateTime: {
      dateValue: string;
      dateFormat: string;
    };
    eventLocation: {
      locationName: string;
      countryName: string;
      unLocode: string;
      countryCode: string | null;
    };
    eventTypeCode: string;
    eventTypeDescription: string;
    inttraBookingReference: string;
    lastKnownCoordinates: any;
    sealNumber: string | null;
    transportationDetails: {
      transportationLocationType: string;
      transportationLocationCode: string;
    }[];
    vesselIMONumber: string | null;
    vesselName: string | null;
    voyageNumber: string | null;
  }[];
  inttraBookingReference: string;
  latestEventDateTime: {
    dateValue: string;
    dateFormat: string;
  };
  latestEventLocation: {
    locationName: string;
    countryName: string;
    unLocode: string;
    countryCode: string | null;
  };
  eventClassifierCode: string;
  emptyIndicatorCode: string;
  emptyIndicatorCodeDescription: string;
  vesselName: string;
  vesselIMONumber: string;
  voyageNumber: string;
  lastKnownCoordinates: any;
  transportationDetails: {
    transportationLocationType: string;
    transportationLocationCode: string;
    transportationDates: {
      transportationDateType: string;
      transportationDateTime: {
        dateValue: string;
        dateFormat: string;
      };
    }[];
  }[];
  comments: string | null;
  sealNumber: string | null;
  latestEventLocationCode: string | null;
}[];
