import React, { useState, useRef, useEffect } from "react";
import { Check, ChevronDown, X } from "lucide-react";
import { COUNTRYLISTISODATA } from "@/constants/countries";
import { useFormContext } from "react-hook-form";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "../ui/form";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "outline" | "default";
  className?: string;
  children: React.ReactNode;
}

interface BadgeProps {
  children: React.ReactNode;
  className?: string;
  onRemove?: (e: React.MouseEvent) => void;
}

interface CommandProps {
  children: React.ReactNode;
  className?: string;
}

interface CommandInputProps {
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
}

interface CommandListProps {
  children: React.ReactNode;
  className?: string;
}

interface CommandEmptyProps {
  children: React.ReactNode;
}

interface CommandGroupProps {
  children: React.ReactNode;
}

interface CommandItemProps {
  children: React.ReactNode;
  onSelect: () => void;
  selected: boolean;
  className?: string;
}

interface PopoverProps {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface PopoverTriggerProps {
  children: React.ReactNode;
  asChild?: boolean;
  onClick?: () => void;
}

interface PopoverContentProps {
  children: React.ReactNode;
  className?: string;
  align?: "start" | "center" | "end";
}

interface MultiSelectProps {
  value?: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
}

const Button: React.FC<ButtonProps> = ({ children, variant = "outline", className = "", onClick, ...props }) => (
  <button
    className={`inline-flex items-center justify-center rounded-md border px-3 py-2 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
      variant === "outline" ? "border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900" : ""
    } ${className}`}
    onClick={onClick}
    {...props}
  >
    {children}
  </button>
);

const Badge: React.FC<BadgeProps> = ({ children, className = "", onRemove }) => (
  <span
    className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-gray-100 text-gray-800 hover:bg-gray-200 ${className}`}
  >
    {children}
    {onRemove && (
      <button
        className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        onClick={onRemove}
        type="button"
      >
        <X className="h-3 w-3" />
      </button>
    )}
  </span>
);

const Command: React.FC<CommandProps> = ({ children, className = "" }) => (
  <div className={`flex h-full w-full flex-col overflow-hidden rounded-md bg-white border ${className}`}>
    {children}
  </div>
);

const CommandInput: React.FC<CommandInputProps> = ({ placeholder, value, onChange, className = "" }) => (
  <div className="flex items-center border-b px-3">
    <input
      className={`flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-gray-500 disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
      placeholder={placeholder}
      value={value}
      onChange={onChange}
    />
  </div>
);

const CommandList: React.FC<CommandListProps> = ({ children, className = "" }) => (
  <div className={`max-h-[300px] overflow-y-auto overflow-x-hidden ${className}`}>{children}</div>
);

const CommandEmpty: React.FC<CommandEmptyProps> = ({ children }) => (
  <div className="py-6 text-center text-sm text-gray-500">{children}</div>
);

const CommandGroup: React.FC<CommandGroupProps> = ({ children }) => (
  <div className="overflow-hidden p-1">{children}</div>
);

const CommandItem: React.FC<CommandItemProps> = ({ children, onSelect, selected, className = "" }) => (
  <div
    className={`relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-100 ${className}`}
    onClick={onSelect}
  >
    <Check className={`mr-2 h-4 w-4 ${selected ? "opacity-100" : "opacity-0"}`} />
    {children}
  </div>
);

// Fixed Popover implementation
const Popover: React.FC<PopoverProps> = ({ children, open, onOpenChange }) => {
  const popoverRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onOpenChange(false);
      }
    };

    if (open) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open, onOpenChange]);

  return (
    <div className="relative" ref={popoverRef}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          if (child.type === PopoverTrigger) {
            return React.cloneElement(child as React.ReactElement<any>, {
              onClick: () => onOpenChange(!open),
            });
          }
          if (child.type === PopoverContent && open) {
            return child;
          }
        }
        return null;
      })}
    </div>
  );
};

const PopoverTrigger: React.FC<PopoverTriggerProps> = ({ children, asChild, onClick }) => (
  <div onClick={onClick}>{children}</div>
);

const PopoverContent: React.FC<PopoverContentProps> = ({ children, className = "", align = "start" }) => (
  <div
    className={`absolute top-full left-0 z-50 mt-1 w-full min-w-[8rem] overflow-hidden rounded-md border bg-white shadow-lg ${className}`}
  >
    {children}
  </div>
);

const MultiSelect: React.FC<MultiSelectProps> = ({ value = [], onChange, placeholder = "Select countries..." }) => {
  const [open, setOpen] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>("");

  const handleSelect = (countryCode: string): void => {
    const newValue = value.includes(countryCode)
      ? value.filter((item) => item !== countryCode)
      : [...value, countryCode];
    onChange(newValue);
  };

  const handleRemove = (countryCode: string): void => {
    const newValue = value.filter((item) => item !== countryCode);
    onChange(newValue);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setSearchValue("");
    }
  };

  const filteredCountries = COUNTRYLISTISODATA.filter((country) =>
    country.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  const selectedCountries = COUNTRYLISTISODATA.filter((country) => value.includes(country.code3));

  return (
    <Popover open={open} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-full justify-between h-auto min-h-[40px] px-3 py-2">
          <div className="flex flex-wrap gap-1 flex-1 text-left">
            {selectedCountries.length === 0 ? (
              <span className="text-gray-500">{placeholder}</span>
            ) : selectedCountries.length === 1 ? (
              <span>{selectedCountries[0].name}</span>
            ) : (
              <div className="flex flex-wrap gap-1">
                {selectedCountries.slice(0, 2).map((country) => (
                  <Badge
                    key={country.code3}
                    className="text-xs"
                    onRemove={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      e.preventDefault();
                      handleRemove(country.code3);
                    }}
                  >
                    {country.name}
                  </Badge>
                ))}
                {selectedCountries.length > 2 && (
                  <Badge className="text-xs">+{selectedCountries.length - 2} more</Badge>
                )}
              </div>
            )}
          </div>
          <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0">
        <Command>
          <CommandInput
            placeholder="Search countries..."
            value={searchValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchValue(e.target.value)}
          />
          <CommandList>
            {filteredCountries.length === 0 ? (
              <CommandEmpty>No country found.</CommandEmpty>
            ) : (
              <CommandGroup>
                {filteredCountries.map((country) => (
                  <CommandItem
                    key={country.number}
                    onSelect={() => handleSelect(country.code3)}
                    selected={value.includes(country.code3)}
                  >
                    {country.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

const MultiSelectCountries: React.FC = () => {
  const { control, watch } = useFormContext();
  console.log(watch("countriesVisitedInBetween"), "thisbine");

  return (
    <FormField
      control={control}
      name="countriesVisitedInBetween"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-gray-700">Countries visited in between</FormLabel>
          <FormControl>
            <MultiSelect value={field.value || []} onChange={field.onChange} placeholder="Choose countries..." />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default MultiSelectCountries;
