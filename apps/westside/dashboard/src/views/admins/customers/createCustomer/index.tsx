import * as Dialog from "@radix-ui/react-dialog";
import { X, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ViewAttachedFile } from "../viewAttachedFile";
import {
  createCustomer,
  getCustomerById,
  editCustomer,
  getCustomerParentCompany,
} from "@/services/admin/adminCustomer";
import { toast } from "sonner";
import { getNames as getCountryNames, getCode as getCountryCode, getData as getData } from "country-list";
import { Country, State } from "country-state-city";
import { ComboBox } from "@/components/ui/comboBox";
import { useNavigate, useParams } from "react-router-dom";

export type CustomerFormValues = {
  firstname: string;
  lastname: string;
  parentCompany: string;
  company_name: string;
  contact: string;
  phoneNo: string;
  email: string;
  notification_email: string;
  zip: string;
  city: string;
  state: string;
  country: string;
  taxId1: string;
  taxId2: string;
  taxId3: string;
  customer_free_form_address: string;
  street_number: string;
  street_name: string;
  po_box: string;
  attachedLicenses?: ({ image: string; file_name: string } | { file: File; image: string; file_name: string })[];
  flagForCustomDocs?: boolean;
};
interface ErrorsState {
  [key: string]: string;
}

export function RightDrawer({
  open,
  onOpenChange,
  mode = "create",
  id,
  refetchListCusomer,
  refetchCountryList,
}: // setMode,
{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode?: "create" | "edit";
  id?: any;
  refetchListCusomer?: () => void;
  refetchCountryList?: () => void;
  // setMode?: (mode: "create" | "edit") => void;
}) {
  const initialFormData: CustomerFormValues = {
    firstname: "",
    lastname: "",
    parentCompany: "",
    company_name: "",
    contact: "",
    phoneNo: "",
    email: "",
    notification_email: "",
    zip: "",
    city: "",
    state: "",
    country: "",
    taxId1: "",
    taxId2: "",
    taxId3: "",
    street_number: "",
    street_name: "",
    po_box: "",
    customer_free_form_address: "",
    attachedLicenses: [],
    flagForCustomDocs: false,
  };
  const [defaultValues, setDefaultValues] = useState<CustomerFormValues>();
  const [formData, setFormData] = useState<CustomerFormValues>(initialFormData);
  const [parentCompany, setParentCompany] = useState([]);
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: countries = [] } = useQuery({
    queryKey: ["countries"],
    queryFn: () => {
      const rawData = getData();
      return rawData
        .map((c) => ({
          label: c.name,
          value: c.code,
        }))
        .sort((a, b) => a.label.localeCompare(b.label));
    },
    staleTime: Infinity,
  });

  const { data: states = [] } = useQuery({
    queryKey: ["states", formData.country],
    queryFn: () => {
      if (!formData.country) return [];
      const selectedCountryCode = formData.country.toUpperCase();

      const fetchedStates = selectedCountryCode ? State.getStatesOfCountry(selectedCountryCode) : [];
      return fetchedStates.sort((a, b) => a.name.localeCompare(b.name));
    },
    enabled: !!formData.country,
  });
  const [errors, setErrors] = useState<ErrorsState>({});

  const { data: customerData, refetch: refetchCustomerData } = useQuery({
    queryKey: ["customer_id", id],
    queryFn: () => getCustomerById(id),
    enabled: !!id && mode === "edit",
  });

  const { data: parentCompanyData } = useQuery({
    queryKey: ["parentCompanies"],
    queryFn: async () => {
      const response = await getCustomerParentCompany();
      return response;
    },
    enabled: open,
  });

  useEffect(() => {
    if (parentCompanyData) {
      setParentCompany(parentCompanyData?.message?.data || []);
    }
  }, [parentCompanyData]);

  useEffect(() => {
    if (!open) {
      formData.attachedLicenses?.forEach((file) => {
        if ("file" in file) {
          URL.revokeObjectURL(file.image);
        }
      });
    }
  }, [open]);

  useEffect(() => {
    if (customerData?.message?.data) {
      const c = customerData.message?.data;
      console.log("test", c);
      const updatedDefaults: CustomerFormValues = {
        firstname: c.first_name || "",
        lastname: c.last_name || "",
        parentCompany: c.parent_company || "",
        company_name: c.company_name || "",
        contact: c.contact || "",
        phoneNo: c.phone || "",
        email: c.email_id || "",
        notification_email: c.notification_email || "",
        zip: c.customer_zip || "",
        city: c.customer_city || "",
        state: c.customer_state || "",
        country: c.customer_country?.code || "",
        taxId1: c.tax_id1 || "",
        taxId2: c.tax_id2 || "",
        taxId3: c.tax_id3 || "",
        street_name: c?.street_name || "",
        street_number: c?.street_number || "",
        po_box: c?.po_box || "",
        customer_free_form_address: c.customer_free_form_address || "",
        attachedLicenses: c.license_attachments || [],
        flagForCustomDocs: c.flag_for_custom_docs === 1 ? true : false,
      };

      setDefaultValues(updatedDefaults);
      setFormData((prev) => ({ ...prev, ...updatedDefaults }));
    }
  }, [customerData]);

  useEffect(() => {
    refetchCustomerData();
    if (open && mode === "edit" && defaultValues) {
      setFormData(defaultValues);
    }
  }, [open, mode, defaultValues]);

  useEffect(() => {
    if (!open) {
      setFormData(initialFormData);
      setErrors({});
    }
  }, [open]);

  useEffect(() => {
    if (open) {
      if (mode === "create") {
        setFormData(initialFormData);
        setErrors({});
      } else if (mode === "edit" && id) {
        refetchCustomerData();
      }
    } else {
      setFormData(initialFormData);
      setErrors({});
      formData.attachedLicenses?.forEach((file) => {
        if ("file" in file) {
          URL.revokeObjectURL(file.image);
        }
      });
    }
  }, [open, mode, id]);
  const handleChange = (field: keyof CustomerFormValues, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
      ...(field === "country" ? { state: "" } : {}),
    }));

    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };
  useEffect(() => {
    if (!open) {
      formData.attachedLicenses?.forEach((file) => {
        if ("license" in file) {
          URL.revokeObjectURL(file.image);
        }
      });
    }
  }, [open]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    const MAX_FILE_SIZE_MB = 10;
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

    const allowedTypes = ["application/pdf", "image/jpeg", "image/png", "image/gif", "image/webp", "image/svg+xml"];

    const newValidAttachments: (typeof formData.attachedLicenses)[number][] = [];
    const invalidTypeFiles: string[] = [];
    const largeFiles: string[] = [];

    Array.from(files).forEach((file) => {
      if (!allowedTypes.includes(file.type)) {
        invalidTypeFiles.push(file.name);
      } else if (file.size > MAX_FILE_SIZE_BYTES) {
        largeFiles.push(file.name);
      } else {
        newValidAttachments.push({
          file: file,
          image: URL.createObjectURL(file),
          file_name: file.name,
        });
      }
    });

    if (invalidTypeFiles.length > 0) {
      toast.error(`Unsupported file types: ${invalidTypeFiles.join(", ")}. Allowed: JPEG, PNG, GIF, WebP, SVG, PDF.`);
    }

    if (largeFiles.length > 0) {
      toast.error(`The following files exceed the ${MAX_FILE_SIZE_MB}MB limit: ${largeFiles.join(", ")}`);
    }

    if (newValidAttachments.length > 0) {
      setFormData((prev) => ({
        ...prev,
        attachedLicenses: [...(prev.attachedLicenses || []), ...newValidAttachments],
      }));
      setErrors((prev) => ({ ...prev, attachedLicenses: "" }));
    }

    e.target.value = ""; // Clear input
  };
  const validateForm = (): boolean => {
    const newErrors: ErrorsState = {};

    // if (!formData.firstname.trim())
    //   newErrors.firstname = "First name is required";
    // if (!formData.lastname.trim()) newErrors.lastname = "Last name is required";
    // if (!formData.parentCompany.trim()) newErrors.parentCompany = "Parent company is required";
    if (!formData.company_name.trim()) newErrors.company_name = "Company name is required";
    if (!formData.contact.trim()) newErrors.contact = "Contact is required";
    if (!formData.phoneNo.trim()) newErrors.phoneNo = "Phone number is required";
    if (!formData.email.trim()) newErrors.email = "Email is required";
    // if (!formData.zip.trim()) newErrors.zip = "Zip code is required";
    // if (!formData.city.trim()) newErrors.city = "City is required";
    // if (!formData.state.trim()) newErrors.state = "State is required";
    // if (!formData.country.trim()) newErrors.country = "Country is required";
    // if (!formData.customer_free_form_address.trim()) newErrors.customer_free_form_address = "Address is required";
    // if (!formData.customer_free_form_address.trim())
    //   newErrors.customer_free_form_address = "Address is required";
    if (!formData.taxId1.trim()) newErrors.taxId1 = "Tax id 1 is required";
    if (!formData.taxId2.trim()) newErrors.taxId2 = "Tax id 2 is required";
    if (!formData.taxId3.trim()) newErrors.taxId3 = "Tax id 3 is required";

    if (!formData.customer_free_form_address.trim()) {
      newErrors.customer_free_form_address = "Address is required";
    } else {
      const lines = formData.customer_free_form_address.split("\n");

      if (lines.length > 4) {
        newErrors.customer_free_form_address = "Address can have max 4 lines";
      } else if (lines.some((line) => line.length > 35)) {
        newErrors.customer_free_form_address = "Each address line can have max 35 characters";
      }
    }
    // const ampCheckFields: { key: keyof CustomerFormValues; label: string }[] = [
    //   { key: "firstname", label: "First name" },
    //   { key: "lastname", label: "Last name" },
    //   { key: "company_name", label: "Company name" },
    //   { key: "customer_free_form_address", label: "Address" },
    //   { key: "contact", label: "Contact" },
    // ];

    // ampCheckFields.forEach(({ key, label }) => {
    //   const value = formData[key];

    //   if (typeof value === "string") {
    //     const trimmed = value.trim();
    //     if (trimmed.includes("&")) {
    //       newErrors[key] = `${label} cannot contain '&'`;
    //     }
    //   }
    // });

    if (formData.firstname.trim()) {
      if (!/[a-zA-Z0-9]/.test(formData.firstname)) {
        newErrors.firstname = "First Name must contain letters or numbers";
      }
    }
    // Format validations
    // if (formData.contact && !/^\d{10}$/.test(formData.contact)) {
    //   newErrors.contact = "Contact number must be 10 digits";
    // }
    // if (formData.phoneNo && !/^\d{10}$/.test(formData.phoneNo)) {
    //   newErrors.phoneNo = "Phone number must be 10 digits";
    // }
    const phoneRegex = /^[\d\s()+-]*$/;

    if (formData.phoneNo && !phoneRegex.test(formData.phoneNo)) {
      newErrors.phoneNo = "Invalid phone number format.";
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Invalid email format";
    }
    if (formData.zip && !/^\d+$/.test(formData.zip)) {
      newErrors.zip = "Zip must be numeric";
    }

    const allowedTypes = [
      "application/pdf",
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "image/svg+xml", // Added SVG as a common image type
    ];

    const MAX_FILE_SIZE_MB = 10;
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

    if (formData.attachedLicenses && formData.attachedLicenses.length > 0) {
      let hasInvalidFile = false;
      let hasLargeFile = false;

      formData.attachedLicenses.forEach((attachment) => {
        if (attachment && attachment.file) {
          if (!allowedTypes.includes(attachment.file.type)) {
            hasInvalidFile = true;
          }
          if (attachment.file.size > MAX_FILE_SIZE_BYTES) {
            hasLargeFile = true;
          }
        }
      });

      if (hasInvalidFile) {
        newErrors.attachedLicenses =
          "Only image files (JPEG, PNG, GIF, WebP, SVG) and PDFs are allowed for attached licenses.";
      }

      if (hasLargeFile) {
        newErrors.attachedLicenses = `Each file must be under ${MAX_FILE_SIZE_MB} MB.`;
      }
    }
    // --- End File Upload Validation ---

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const isValid = validateForm();
    if (!isValid) {
      toast.error("Please check all the fields.");
      return;
    }
    if (mode === "edit") {
      console.log("Updating customer:", formData);

      try {
        const licenseImages = formData.attachedLicenses?.map((item) => {
          if ("file" in item) {
            return item.file; // new file
          }
          return { image_url: item.image, file_name: item.file_name }; // existing file
        });

        const response = await editCustomer({
          customer_id: id,
          licenseAttachments: licenseImages || [],
          data: {
            first_name: formData.firstname,
            last_name: formData.lastname,
            customer_name: formData.company_name,
            parent_company: formData.parentCompany,
            company_name: formData.company_name,
            contact: formData.contact,
            phone: formData.phoneNo,
            email_id: formData.email,
            customer_zip: formData.zip,
            customer_city: formData.city,
            customer_state: formData.state,
            customer_country: formData.country,
            tax_id1: formData.taxId1,
            tax_id2: formData.taxId2,
            tax_id3: formData.taxId3,
            street_name: formData?.street_name,
            street_number: formData.street_number,
            po_box: formData.po_box,
            customer_free_form_address: formData.customer_free_form_address,
            notification_email: formData.notification_email,
            flag_for_custom_docs: formData.flagForCustomDocs ? 1 : 0,
          },
        });

        if (response?.message?.status === 200) {
          toast.success(response?.message?.message || "Customer edited successfully");
          refetchListCusomer && refetchListCusomer();
          setFormData(initialFormData);
          onOpenChange(false);
          // setMode && setMode("create");
          refetchListCusomer && refetchListCusomer();
          refetchCountryList && refetchCountryList();
        } else {
          toast.error(response?.message?.message || "Failed to update customer");
        }
      } catch (error: any) {
        console.error("Failed to update customer:", error);
        toast.error(error?.response?.data?.message?.error || "Failed to update customer");
      }
    } else {
      try {
        setIsSubmitting(true);
        const response = await createCustomer(
          {
            first_name: formData.firstname,
            last_name: formData.lastname,
            customer_name: formData.firstname,
            parent_company: formData.parentCompany,
            company_name: formData.company_name,
            contact: formData.contact,
            phone: formData.phoneNo,
            email_id: formData.email,
            customer_zip: formData.zip,
            customer_city: formData.city,
            customer_state: formData.state,
            customer_country: formData.country,
            tax_id1: formData.taxId1,
            tax_id2: formData.taxId2,
            tax_id3: formData.taxId3,
            street_name: formData?.street_name,
            street_number: formData.street_number,
            po_box: formData.po_box,
            customer_free_form_address: formData.customer_free_form_address,
            notification_email: formData.notification_email,
            flag_for_custom_docs: formData.flagForCustomDocs ? 1 : 0,
          },
          formData.attachedLicenses?.map((f) => ("file" in f ? f.file : null)).filter(Boolean) as File[]
        );
        if (response?.message?.status_code === 200 || response?.message?.status_code === 201) {
          onOpenChange(false);
          toast.success("Customer created successfully");
          refetchListCusomer && refetchListCusomer();
          refetchCountryList && refetchCountryList();
          navigate(`/dashboard/customers/list`);
        } else {
          toast.error(response?.message?.message || "Failed to create customer");
        }
      } catch (error: any) {
        console.error("Create customer failed:", error);
        toast.error(error?.response?.data?.message?.error || "Failed to create customer");
      } finally {
        setIsSubmitting(false); // Stop loading
      }
    }
  };

  const [previewOpen, setPreviewOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<{
    name: string;
    license: string;
  } | null>(null);

  useEffect(() => {
    if (formData.country && states.length > 0) {
      const stateExists = states.find((s) => s.isoCode === formData.state);
      if (!stateExists) {
        setFormData((formData) => ({ ...formData, state: "" }));
      }
    }
  }, [states, formData.state, formData.country]);
  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-40" />
        <Dialog.Content
          className="fixed right-0 top-0 h-full w-full max-w-4xl bg-white z-50 overflow-y-auto transition-all"
          onPointerDownOutside={(e) => e.preventDefault()}
        >
          <div className="p-4 border-b flex justify-between items-center sticky top-0 bg-white z-10">
            <h2 className="text-lg font-medium">{mode === "edit" ? "Edit Customer" : "Create Customer"}</h2>
            <Dialog.Close asChild>
              <Button variant="ghost" size="icon">
                <X className="w-5 h-5" />
              </Button>
            </Dialog.Close>
          </div>

          <form className="p-6 space-y-6" onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <InputField
                label={<>First Name</>}
                value={formData.firstname}
                onChange={(v) => handleChange("firstname", v)}
                error={errors.firstname}
              />
              <InputField
                label={<>Last Name</>}
                value={formData.lastname}
                onChange={(v) => handleChange("lastname", v)}
                error={errors.lastname}
              />
              <InputField
                label={
                  <>
                    Company Name <span className="text-red-500">*</span>
                  </>
                }
                value={formData.company_name}
                onChange={(v) => handleChange("company_name", v)}
                error={errors.company_name}
              />

              {/* <SelectField
                label="Parent Company"
                value={formData.parentCompany}
                onChange={(v) => handleChange("parentCompany", v)}
                options={
                  Array.isArray(parentCompany) && parentCompany.length > 0
                    ? parentCompany.map((company: any) => ({
                        value: company,
                        label: company,
                      }))
                    : []
                }
              /> */}

              <ComboBox
                label="Parent Company"
                required={false}
                value={formData.parentCompany}
                onChange={(v) => handleChange("parentCompany", v)}
                options={
                  Array.isArray(parentCompany) && parentCompany.length > 0
                    ? parentCompany
                        .slice()
                        .sort((a: any, b: any) => a.localeCompare(b))
                        .map((company: any) => company)
                    : []
                }
                placeholder="Select Parent Company"
              />
              <InputField
                label={
                  <>
                    Contact <span className="text-red-500">*</span>
                  </>
                }
                value={formData.contact}
                onChange={(v) => handleChange("contact", v)}
                error={errors.contact}
              />

              <InputField
                label={
                  <>
                    Phone No <span className="text-red-500">*</span>
                  </>
                }
                value={formData.phoneNo}
                onChange={(v) => handleChange("phoneNo", v)}
                error={errors.phoneNo}
              />
              <InputField
                label={
                  <>
                    Email ID <span className="text-red-500">*</span>
                  </>
                }
                value={formData.email}
                onChange={(v) => handleChange("email", v)}
                error={errors.email}
              />
              <InputField
                label={<>Notification Email</>}
                value={formData.notification_email}
                onChange={(v) => handleChange("notification_email", v)}
                error={errors.notification_email}
              />
              <InputField
                label={
                  <>
                    Tax ID 1 <span className="text-red-500">*</span>
                  </>
                }
                value={formData.taxId1 || ""}
                onChange={(v) => handleChange("taxId1", v)}
                error={errors.taxId1}
              />
              <InputField
                label={
                  <>
                    Tax ID 2 <span className="text-red-500">*</span>
                  </>
                }
                value={formData.taxId2 || ""}
                error={errors.taxId2}
                onChange={(v) => handleChange("taxId2", v)}
              />
              <InputField
                label={
                  <>
                    Tax ID 3 <span className="text-red-500">*</span>
                  </>
                }
                value={formData.taxId3 || ""}
                onChange={(v) => handleChange("taxId3", v)}
                error={errors.taxId3}
              />
              <InputField
                label={<>Street Name</>}
                value={formData.street_name || ""}
                onChange={(v) => handleChange("street_name", v)}
                error={errors.street_name}
              />
              <InputField
                label={<>Street Number</>}
                value={formData.street_number || ""}
                onChange={(v) => handleChange("street_number", v)}
                error={errors.street_number}
              />
              <InputField
                label={<>PO BOX</>}
                value={formData.po_box || ""}
                onChange={(v) => handleChange("po_box", v)}
                error={errors.po_box}
              />
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="col-span-3">
                <label className="block mb-1 text-sm font-medium">
                  Customer-free form address <span className="text-red-500">*</span>
                </label>
                <Textarea
                  value={formData.customer_free_form_address}
                  onChange={(e) => handleChange("customer_free_form_address", e.target.value)}
                  rows={4}
                />
                {errors.customer_free_form_address && (
                  <p className="text-red-500 text-sm mt-1">{errors.customer_free_form_address}</p>
                )}
              </div>
              <ComboBox
                label="Country"
                value={
                  countries.find((s) => s.value.toLowerCase() === formData.country?.toLowerCase())?.label ||
                  formData.country
                }
                onChange={(selectedName) => {
                  const selected = countries.find((s) => s.label === selectedName);
                  if (selected) {
                    handleChange("country", selected.value);
                  }
                }}
                options={countries.map((c) => c.label)}
                error={errors.country}
                placeholder="Select Country"
              />
              <ComboBox
                label="State"
                value={states.find((s) => s.isoCode === formData.state)?.name || ""}
                onChange={(selectedName) => {
                  const selected = states.find((s) => s.name === selectedName);
                  if (selected) {
                    handleChange("state", selected.isoCode);
                  }
                }}
                options={states.map((state) => state.name)}
                error={errors.state}
                placeholder="Select State"
              />
              <InputField
                label={<>City</>}
                value={formData.city || ""}
                onChange={(v) => handleChange("city", v)}
                error={errors.city}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={formData.flagForCustomDocs}
                onCheckedChange={(checked) => handleChange("flagForCustomDocs", checked === true)}
              />
              <label className="text-sm font-medium">Is ISCC Docs Required</label>
            </div>

            <div>
              <label className="block mb-1 text-sm font-medium text-gray-700">
                Attached License{" "}
                <span className="text-sm font-normal text-gray-500">
                  (Only JPEG, PNG, GIF, WebP, SVG, or PDF files are allowed)
                </span>
              </label>
              {/* <div className="flex items-center gap-4">
                <Input
                  type="file"
                  multiple
                  onChange={handleFileChange}
                  className="cursor-pointer text-gray-500 w-auto"
                />

                {formData.attachedLicenses &&
                  formData.attachedLicenses.length > 0 && (
                    <span className="text-sm font-normal text-gray-500 whitespace-nowrap">
                      {formData.attachedLicenses.length} file
                      {formData.attachedLicenses.length > 1 ? "s" : ""} uploaded
                    </span>
                  )}
              </div> */}
              <div className="flex items-center gap-4">
                <div className="relative overflow-hidden inline-block">
                  <button
                    type="button"
                    className="bg-gray-100 text-gray-700 px-4 py-2 rounded border border-gray-300 hover:bg-gray-200 text-sm"
                  >
                    Choose files
                  </button>
                  <input
                    type="file"
                    multiple
                    onChange={handleFileChange}
                    className="absolute top-0 left-0 opacity-0 cursor-pointer w-full h-full"
                  />
                </div>

                {formData.attachedLicenses && formData.attachedLicenses.length > 0 && (
                  <span className="text-sm font-normal text-gray-500 whitespace-nowrap">
                    {formData.attachedLicenses.length} file
                    {formData.attachedLicenses.length > 1 ? "s" : ""} uploaded
                  </span>
                )}
              </div>
              {errors.attachedLicenses && <p className="text-red-500 text-sm mt-1">{errors.attachedLicenses}</p>}
              {formData.attachedLicenses && formData.attachedLicenses.length > 0 && (
                <ul className="mt-2 space-y-2 text-sm text-blue-600">
                  {formData.attachedLicenses.map((item, idx) => (
                    <li key={idx} className="flex justify-between items-center">
                      <span
                        className="cursor-pointer hover:underline"
                        onClick={() => {
                          // setSelectedFile({ name: item.name, license: item.license});
                          setSelectedFile({
                            name: item.file_name || `Attachment ${idx + 1}`,
                            license: item.image,
                          });
                          setPreviewOpen(true);
                        }}
                      >
                        {/* {item.name} */}
                        {item.file_name || `Attachment ${idx + 1}`}
                      </span>
                      <button
                        type="button"
                        className="ml-2 text-red-600 hover:text-red-800"
                        onClick={() => {
                          const updated = formData.attachedLicenses!.filter((_, i) => i !== idx);
                          setFormData((prev) => ({
                            ...prev,
                            attachedLicenses: updated,
                          }));
                        }}
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            <div className="flex justify-end pt-4">
              <Button type="submit" className="bg-foreground" variant="default">
                {mode === "edit" ? (isSubmitting ? "Updating..." : "Update") : isSubmitting ? "Saving..." : "Save"}
              </Button>
            </div>
          </form>
        </Dialog.Content>
      </Dialog.Portal>

      <ViewAttachedFile
        open={previewOpen}
        selectedFile={selectedFile}
        onOpenChange={setPreviewOpen}
        setSelectedFile={setSelectedFile}
      />
    </Dialog.Root>
  );
}

// Reusable Input Field
function InputField({
  label,
  value,
  onChange,
  error,
}: {
  label: React.ReactNode;
  value: string;
  onChange: (val: string) => void;
  error?: string;
}) {
  return (
    <div>
      <label className="block mb-1 text-sm font-medium">{label}</label>
      <Input value={value} onChange={(e) => onChange(e.target.value)} className={error ? "border-red-500" : ""} />
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
}

// Reusable Select Field
function SelectField({
  label,
  value,
  onChange,
  options,
  error,
}: {
  label: React.ReactNode;
  value: string;
  onChange: (val: string) => void;
  options: { value: string; label: string }[];
  error?: string;
}) {
  return (
    <div>
      <label className="block mb-1 text-sm font-medium">{label}</label>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger>
          <SelectValue placeholder="Select option" />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
    </div>
  );
}
