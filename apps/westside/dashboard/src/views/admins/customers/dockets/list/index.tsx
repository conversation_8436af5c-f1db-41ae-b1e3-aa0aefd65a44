import { Input } from "@/components/ui/input";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import {
  Search,
  Eye,
  ChevronLeft,
  ChevronRight,
  FileSearch,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";

import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { useNavigate } from "react-router-dom";
import { fetchDockets } from "@/services/admin/Dockets/docket-list";
import { useQuery } from "@tanstack/react-query";
import Loader from "@/components/Loader";

const DocketList = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [page, setPage] = useState(1);
  const pageSize = 10;

  const { data, isLoading, isError, error } = useQuery({
    queryKey: [
      "dockets",
      {
        status: statusFilter === "all" ? "" : statusFilter,
        search: searchTerm,
        page,
        page_size: pageSize.toString(),
      },
    ],
    queryFn: () =>
      fetchDockets({
        status: statusFilter === "all" ? "" : statusFilter,
        search: searchTerm,
        page: page,
        page_size: pageSize.toString(),
      }),
    keepPreviousData: true,
  });

  const Docket_list = data?.message?.docket || [];

  const totalPages = data?.message?.total_pages || 1;

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(1);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    setPage(1);
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString === "N/A") return "N/A";
    try {
      const date = new Date(dateString);
      const month = date.toLocaleString("default", { month: "short" });
      const day = date.getDate().toString().padStart(2, "0");
      const year = date.getFullYear();
      return `${month}-${day}-${year}`;
    } catch (e) {
      return dateString.split(" ")[0];
    }
  };

  const getStatusColorGradient = (status: string) => {
    switch (status) {
      case "New":
        return "#DBEAFE";
      case "Rejected":
        return "#FEE2E2";
      case "Accepted":
        return "#DCFCE7";
      case "Acknowledged":
        return "#FFEDD5";
      case "Reopen":
        return "#FEF9C3";
      case "Open":
        return "#EDE9FE";
      case "Sent":
        return "#E9E294";
      case "Revised":
        return "#E0F7FA";
      default:
        return "#F3F4F6";
    }
  };

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case "New":
        return "#1D4ED8";
      case "Rejected":
        return "#B91C1C";
      case "Accepted":
        return "#15803D";
      case "Acknowledged":
        return "#C2410C";
      case "Reopen":
        return "#A16207";
      case "Open":
        return "#6D28D9";
      case "Sent":
        return "#2F855A";
      case "Revised":
        return "#17A2B8";
      default:
        return "#374151";
    }
  };

  const getStatusBorderColor = (status: string) => {
    switch (status) {
      case "New":
        return "#3B82F6";
      case "Rejected":
        return "#EF4444";
      case "Accepted":
        return "#22C55E";
      case "Acknowledged":
        return "#F97316";
      case "Reopen":
        return "#EAB308";
      case "Open":
        return "#8B5CF6";
      case "Sent":
        return "#2F855A";
      case "Revised":
        return "#17A2B8";
      default:
        return "#9CA3AF";
    }
  };

  const renderPageNumbers = () => {
    if (!totalPages) return null;

    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, page - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Always show first page
    if (startPage > 1) {
      pages.push(
        <Button
          key={1}
          className={`rounded-lg px-3 py-2 ${
            page === 1 ? "bg-white text-black border" : "text-gray-500"
          }`}
          variant="outline"
          onClick={() => setPage(1)}
          disabled={isLoading}
        >
          1
        </Button>
      );
      if (startPage > 2) {
        pages.push(
          <span key="start-ellipsis" className="px-2 text-gray-500">
            ...
          </span>
        );
      }
    }

    // Show visible pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Button
          key={i}
          className={`rounded-lg px-3 py-2 ${
            page === i ? "bg-white text-black border" : "text-gray-500"
          }`}
          variant="outline"
          onClick={() => setPage(i)}
          disabled={isLoading}
        >
          {i}
        </Button>
      );
    }

    // Always show last page
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(
          <span key="end-ellipsis" className="px-2 text-gray-500">
            ...
          </span>
        );
      }
      pages.push(
        <Button
          key={totalPages}
          className={`rounded-lg px-3 py-2 ${
            page === totalPages ? "bg-white text-black border" : "text-gray-500"
          }`}
          variant="outline"
          onClick={() => setPage(totalPages)}
          disabled={isLoading}
        >
          {totalPages}
        </Button>
      );
    }

    return pages;
  };

  if (isError) {
    return (
      <div>
        Error: {error instanceof Error ? error.message : "Unknown error"}
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between">
        <div className="flex gap-4 items-center ml-auto">
          <div className="relative">
            <Input
              className="pr-10"
              placeholder="Search here"
              value={searchTerm}
              onChange={handleSearchChange}
            />
            <Search className="absolute right-4 top-[14px] h-4 w-4 text-gray-400" />
          </div>

          <div className="relative flex">
            <Select onValueChange={handleStatusChange} value={statusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Select status</SelectLabel>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="New">New</SelectItem>
                  <SelectItem value="Rejected">Rejected</SelectItem>
                  <SelectItem value="Acknowledged">Acknowlged</SelectItem>
                  <SelectItem value="Open">Open</SelectItem>
                  <SelectItem value="Sent">Sent</SelectItem>
                  <SelectItem value="Accepted">Accepted</SelectItem>
                  <SelectItem value="Reopened">Reopened</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      <div className="pt-6 overflow-y-auto">
        {isLoading ? (
          <div className="flex justify-center items-center h-[400px]">
            <Loader />
          </div>
        ) : (
          <ScrollArea className="whitespace-nowrap">
            <Table className="w-full border-separate border-spacing-y-2">
              <TableHeader>
                <TableRow className="bg-[#D3DAE7] h-[55px]">
                  <TableHead className="p-2 text-center font-bold text-[#191C36] min-w-[20px]"></TableHead>
                  <TableHead className="p-2 text-center font-bold text-[#191C36] min-w-[80px]">
                    DOCKETS ID
                  </TableHead>
                  <TableHead className="p-2 text-center font-bold text-[#191C36] min-w-[80px]">
                    CARRIER
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[120px]">
                    CUSTOMER NAME
                  </TableHead>
                  <TableHead className="p-2 text-center font-bold text-[#191C36] min-w-[100px]">
                    BOOKING ID
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[100px]">
                    BL NO
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[80px]">
                    POL
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[80px]">
                    POD
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[100px]">
                    <div className="flex flex-col">
                      <span>CONT</span>
                      <span>QTY</span>
                    </div>
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[80px]">
                    WEIGHT
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[100px]">
                    <div className="flex flex-col">
                      <span>REVISION </span>
                      <span>NO</span>
                    </div>
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[80px]">
                    ETA
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[80px]">
                    INVOICE
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[100px]">
                    <div className="flex flex-col">
                      <span>INVOICE </span>
                      <span>TOTAL</span>
                    </div>
                  </TableHead>
                  <TableHead className="p-2 text-left font-bold text-[#191C36] min-w-[100px]">
                    STATUS
                  </TableHead>
                  <TableHead className="p-2 text-center font-bold text-[#191C36] min-w-[80px]">
                    ACTION
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="border-b border-gray-300 h-[75px]">
                {Docket_list && Docket_list.length > 0 ? (
                  Docket_list.map((item: any) => (
                    <TableRow
                      key={item.id}
                      className="border border-[#D3DAE7] hover:bg-gray-300 transition-all duration-300 transform shadow-md"
                      style={{ border: `2px solid #D3DAE7` }}
                    >
                      <TableCell className="p-2 border-t-2 border-l-2 border-b-2 text-left"></TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.id || "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.carrier || "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        <div className="flex items-left justify-left gap-2">
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {item.customer_id || "N/A"}
                            </span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.carrier_booking_number || "N/A"}
                      </TableCell>
                       <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.bol_number || "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.port_of_origin?.location_name ||
                          item.port_of_origin ||
                          "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.place_of_receipt?.location_name ||
                          item.place_of_receipt ||
                          "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.container_qty || item.container_qty || "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.weight || item.weight || "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.revision || "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {formatDate(item.ETA) || "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.invoice_no || "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        {item.invoice_total !== undefined &&
                        item.invoice_total !== null
                          ? Number(item.invoice_total).toFixed(2)
                          : "N/A"}
                      </TableCell>
                      <TableCell className="p-2 text-bold border-t-2 border-b-2 text-left">
                        <div className="flex justify-left">
                          <span
                            className={`px-2 py-1 rounded-sm text-sm font-normal`}
                            style={{
                              color: getStatusTextColor(item.status),
                              borderColor: getStatusBorderColor(item.status),
                              backgroundColor: getStatusColorGradient(
                                item.status
                              ),
                              borderWidth: "2px",
                              borderStyle: "solid",
                            }}
                          >
                            {item.status === "Acknowledged"
                              ? "Acknowledged"
                              : item.status === "Reopen"
                              ? "Reopened"
                              : item.status || "N/A"}
                          </span>
                        </div>
                      </TableCell>

                      <TableCell className="p-2 text-bold border-t-2 border-b-2 border-r-2 text-left">
                        <div className="flex justify-center gap-1">
                          <Button
                            variant="outline"
                            className="border-1 border-primary bg-primary hover:bg-orange-600 rounded-lg flex gap-2 p-5"
                            onClick={() =>
                              navigate(
                                `/dashboard/customers/customer-docket-view/${item.id}?carrierBooking=${item.carrier_booking_number}`
                              )
                            }
                          >
                            <Eye className="text-white" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={15} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center gap-2">
                        <FileSearch className="w-10 h-10 text-gray-400" />
                        <span className="text-gray-500 font-medium">
                          No data found
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        )}
      </div>
      {totalPages > 1 && (
        <div className="flex items-center justify-between sm:justify-between w-full">
          <div className="flex items-center gap-2 mt-6 ml-auto">
            <Button
              className="rounded-lg px-3 py-2"
              variant="outline"
              onClick={() => setPage(1)}
              disabled={page === 1 || isLoading}
            >
              <ChevronsLeft className="text-black" />
            </Button>
            <Button
              className="rounded-lg px-3 py-2"
              variant="outline"
              onClick={() => setPage((p) => Math.max(1, p - 1))}
              disabled={page === 1 || isLoading}
            >
              <ChevronLeft className="text-black" />
            </Button>

            {renderPageNumbers()}

            <Button
              className="rounded-lg px-3 py-2"
              variant="outline"
              onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
              disabled={page >= totalPages || isLoading}
            >
              <ChevronRight className="text-black" />
            </Button>
            <Button
              className="rounded-lg px-3 py-2"
              variant="outline"
              onClick={() => setPage(totalPages)}
              disabled={page === totalPages || isLoading}
            >
              <ChevronsRight className="text-black" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocketList;
