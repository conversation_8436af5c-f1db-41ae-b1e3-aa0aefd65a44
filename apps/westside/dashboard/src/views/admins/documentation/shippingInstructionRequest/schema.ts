import { z } from "zod";

const commaSeparatedEmails = z
  .string()
  .trim()
  .refine(
    (value) => {
      if (!value) return true; // allow optional
      const emails = value.split(",").map((e) => e.trim());
      if (emails.length > 6) return false;
      const emailRegex = /^[\w.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/;
      return emails.every((email) => emailRegex.test(email));
    },
    {
      message: "Enter up to 6 valid email addresses separated by commas",
    }
  );

const twoDigitOptional = z
  .union([z.literal(""), z.string().regex(/^([1-9][0-9]?)$/, "Must be a number between 1 and 99")])
  .optional();

export const SIRequestFormSchema = z
  .object({
    bookingNumber: z.string().optional(),
    creationDate: z.string().optional(),
    bookingRequestId: z.string().optional(),

    // top parties general details.
    shipper: z.string(),
    customShipperName: z.string().nonempty("Shipper cannot be empty!"),
    shipperEmail: z.string(),
    shipperMobileNumber: z.string(),
    shipperAddress: z.string().nonempty("Shipper address cannot be empty!"),
    forwarder: z.string(),
    forwarderName: z.string().optional(),
    forwarderAddress: z.string().optional(),

    // bottom partier general details.
    consignee: z.string(),
    consigneeName: z.string().nonempty("Consignee cannot be empty!"),
    consigneeAddress: z.string().nonempty("Consignee Address cannot be empty!"),
    notifyParty: z.string(),
    notifyPartyName: z.string().nonempty("Notify party cannot be empty!"),
    notifyPartyAddress: z.string().nonempty("Notify party address cannot be empty!"),

    // references.
    shipperReferenceNumber: z.string().optional(),
    forwarderReferenceNumber: z.string().optional(),
    transactionReferenceNumber: z.string().nonempty("Transaction Ref number cannot be empty!"),
    uniqueConsignmentReferenceNumber: z.string().optional(),
    purchaseOrderNumber: z.string().optional(),
    contractReferenceNumber: z.string().optional(),
    BLReferenceNumber: z.string().optional(),
    exportersReferenceNumbers: z.string().optional(),
    consigneeOrderNumber: z.string().optional(),
    invoiceReferenceNumber: z.string().optional(),
    letterOfCreditReferenceNumber: z.string().optional(),
    letterOfCreditIssueDate: z.string().optional(),
    letterOfCreditExpiryDate: z.string().optional(),
    customsHouseBrokerReferenceNumber: z.string().optional(),
    govtReferenceOrFMCNumber: z.string().optional(),
    exportLicenseNumber: z.string().optional(),
    exportLicenseIssueDate: z.string().optional(),
    exportLicenseExpiryDate: z.string().optional(),

    // carrier Tab section.
    carrier: z.string().nonempty("Carrier cannot be empty!"),
    carrierBookingNumber: z
      .string()
      .nonempty("Carrier booking number cannot be empty!")
      .min(2, "Please enter atleast 2 characters."),
    UCACarrier: z.string(),
    UCAEmail: z.string(),
    UCACarrierBookingNumber: z.string(),

    // additional Parties.
    additionalNotifyParty1: z.string().optional(),
    additionalNotifyParty1Name: z.string().optional(),
    additionalNotifyParty1Address: z.string().optional(),
    additionalNotifyParty2: z.string().optional(),
    additionalNotifyParty2Name: z.string().optional(),
    additionalNotifyParty2Address: z.string().optional(),
    contractParty: z.string().optional(),
    contractPartyName: z.string().optional(),
    contractPartyAddress: z.string().optional(),
    freightPayer: z.string().optional(),
    freightPayerAddress: z.string().optional(),
    supplier: z.string().optional(),
    supplierAddress: z.string().optional(),
    consolidator: z.string().optional(),
    consolidatorAddress: z.string().optional(),
    importer: z.string().optional(),
    importerAddress: z.string().optional(),
    warehouseKeeper: z.string().optional(),
    warehouseKeeperAddress: z.string().optional(),

    // transport.
    vessel: z.string().nonempty("Vessel cannot be empty!"),
    voyage: z.string().nonempty("Voyage cannot be empty!"),
    IMONumber: z.string().max(9, "IMO Number cannot exceed 9 characters.").optional(),
    placeOfCarrierReceipt: z
      .object({
        name: z.string(),
        location: z.string(),
        locode: z.string(),
      })
      .refine((data) => data.name && data.location && data.locode, {
        message: "Place of carrier receipt cannot be empty!",
        path: [],
      }),
    placeOfCarrierReceiptBLas: z.string().optional(),
    portOfLoad: z
      .object({
        name: z.string(),
        location: z.string(),
        locode: z.string(),
      })
      .refine((data) => data.name && data.location && data.locode, {
        message: "Port of load cannot be empty!",
        path: [],
      }),
    portOfLoadBLas: z.string().optional(),
    portOfDischarge: z
      .object({
        name: z.string(),
        location: z.string(),
        locode: z.string(),
      })
      .refine((data) => data.name && data.location && data.locode, {
        message: "Place of discharge cannot be empty!",
        path: [],
      }),
    portOfDischargeBLas: z.string().optional(),
    placeOfCarrierDelivery: z
      .object({
        name: z.string(),
        location: z.string(),
        locode: z.string(),
      })
      .refine((data) => data.name && data.location && data.locode, {
        message: "Place of carrier delivery cannot be empty!",
        path: [],
      }),
    placeOfCarrierDeliveryBLas: z.string().optional(),
    moveType: z.string().nonempty("Move type cannot be empty!"),
    shipmentType: z.string().nonempty("Move type cannot be empty!"),

    // Customs Compliance.
    shipperTaxId: z.string().nonempty("Shipper Tax Id cannot be empty!"),
    consigneeTaxId: z.string().nonempty("Consignee Tax Id cannot be empty!"),
    notifyPartyTaxId: z.string().nonempty("Notify Party Tax Id cannot be empty!"),
    printTaxIdOnBillOfLaiding: z.string().optional(),

    // ICS2 Summary Declaration.
    goodsDeliveriedCountry: z.string().nonempty("Choose any of the value!"),
    typeOfDeclaration: z.string().optional(),
    shipmentHouseBillStatus: z.string().optional(),
    pcin: z.string().optional(),
    csn: z.string().optional(),
    acidNumber: z.string().optional(),
    // House bill Modal.
    housebillOfLadingNumber: z.string().optional(),
    placeOfAcceptance: z
      .object({
        name: z.string(),
        location: z.string(),
        locode: z.string(),
      })
      .optional(),
    placeOfFinalDelivery: z
      .object({
        name: z.string(),
        location: z.string(),
        locode: z.string(),
      })
      .optional(),
    firstCountry: z.string().optional(),
    countriesVisitedInBetween: z.array(z.string()).optional(),
    lastCountry: z.string().optional(),
    methodOfPayment: z.string().optional(),
    shipperPartyInvolved: z
      .object({
        actualPartyInformationSameAs: z.string().optional(),
        partyId: z.string().optional(),
        actualParty: z.string().optional(),
        streetNumber: z.string().optional(),
        contactName: z.string().optional(),
        poBOX: z.string().optional(),
        streetName: z.string().optional(),
        state: z.string().optional(),
        city: z.string().optional(),
        country: z.string().optional(),
        postalCode: z.string().optional(),
        taxID: z.string().optional(),
        eori: z.string().optional(),
      })
      .optional(),
    consigneePartyInvolved: z
      .object({
        actualPartyInformationSameAs: z.string().optional(),
        partyId: z.string().optional(),
        actualParty: z.string().optional(),
        streetNumber: z.string().optional(),
        contactName: z.string().optional(),
        poBOX: z.string().optional(),
        streetName: z.string().optional(),
        state: z.string().optional(),
        city: z.string().optional(),
        country: z.string().optional(),
        postalCode: z.string().optional(),
        taxID: z.string().optional(),
        eori: z.string().optional(),
      })
      .optional(),
    notifyPartyInvolved: z
      .object({
        actualPartyInformationSameAs: z.string().optional(),
        partyId: z.string().optional(),
        actualParty: z.string().optional(),
        streetNumber: z.string().optional(),
        contactName: z.string().optional(),
        poBOX: z.string().optional(),
        streetName: z.string().optional(),
        state: z.string().optional(),
        city: z.string().optional(),
        country: z.string().optional(),
        postalCode: z.string().optional(),
        taxID: z.string().optional(),
        eori: z.string().optional(),
      })
      .optional(),

    // particulars.
    isShippingSingleCargoOnly: z.boolean(),
    particularContainers: z.array(
      z.object({
        // container details.
        containerNumber: z
          .string()
          .nonempty("Container number cannot be empty!")
          .max(11, {
            message: "Container number must not exceed 11 characters",
          })
          .refine((val) => /^[A-Za-z]{4}/.test(val), {
            message: "First 4 characters must be alphabets only",
          })
          .refine((val) => /^\w+$/.test(val), {
            message: "Only alphabets and digits are allowed (no special characters)",
          })
          .refine(
            (val) => {
              const digitsPart = val.slice(4);
              return /^\d{6,7}$/.test(digitsPart);
            },
            {
              message: "Last 6 or 7 characters must be digits",
            }
          ),
        containerType: z.object({ name: z.string(), type: z.string() }).refine((data) => data.name && data.type, {
          message: "Container type cannot be empty!",
          path: [],
        }),
        containerSupplier: z.string().optional(),
        containerTareWeight: z.object({
          weight: z.string().optional(),
          unit: z.string().optional(),
        }),
        woodDeclaration: z.string().optional(),
        carrierSealNumber: z
          .string()
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              // Check for trailing comma
              return !val.trim().endsWith(",");
            },
            {
              message: "Trailing comma is not allowed",
            }
          )
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              // Check for consecutive commas
              return !val.includes(",,");
            },
            {
              message: "Consecutive commas are not allowed",
            }
          )
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              const parts = val
                .split(",")
                .map((p) => p.trim())
                .filter(Boolean);

              // Maximum 5 parts
              return parts.length <= 5;
            },
            {
              message: "Maximum of 5 seal numbers allowed",
            }
          )
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              const parts = val
                .split(",")
                .map((p) => p.trim())
                .filter(Boolean);

              // Each part must be at least 2 characters
              return parts.every((p) => p.length >= 2);
            },
            {
              message: "Each seal number must have at least 2 characters",
            }
          )
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              const parts = val
                .split(",")
                .map((p) => p.trim())
                .filter(Boolean);

              // Each part must be alphanumeric only
              return parts.every((p) => /^[A-Za-z0-9]+$/.test(p));
            },
            {
              message: "Seal numbers must be alphanumeric and non-empty",
            }
          )
          .optional(),
        shipperSealNumber: z
          .string()
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              // Check for trailing comma
              return !val.trim().endsWith(",");
            },
            {
              message: "Trailing comma is not allowed",
            }
          )
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              // Check for consecutive commas
              return !val.includes(",,");
            },
            {
              message: "Consecutive commas are not allowed",
            }
          )
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              const parts = val
                .split(",")
                .map((p) => p.trim())
                .filter(Boolean);

              // Maximum 5 parts
              return parts.length <= 5;
            },
            {
              message: "Maximum of 5 seal numbers allowed",
            }
          )
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              const parts = val
                .split(",")
                .map((p) => p.trim())
                .filter(Boolean);

              // Each part must be at least 2 characters
              return parts.every((p) => p.length >= 2);
            },
            {
              message: "Each seal number must have at least 2 characters",
            }
          )
          .refine(
            (val) => {
              // If empty or only whitespace, it's valid since it's optional
              if (!val || val.trim() === "") return true;

              const parts = val
                .split(",")
                .map((p) => p.trim())
                .filter(Boolean);

              // Each part must be alphanumeric only
              return parts.every((p) => /^[A-Za-z0-9]+$/.test(p));
            },
            {
              message: "Seal numbers must be alphanumeric and non-empty",
            }
          )
          .optional(),
        containerTerminalOperatorSealNumber: z.string().optional(),
        containerVeterinarySealNumber: z.string().optional(),
        containerCustomsSealNumber: z.string().optional(),
        containerCustomsReleaseCode: z.string().optional(),
        containerStuffingLocation: z.string().optional(),
        containerComments: z.string().optional(),
        // cargo details.
        cargoPackageCount: z.string().nonempty("Count required!"),
        cargoPackageType: z
          .object({
            xmlCode: z.string(),
            shortName: z.string(),
            name: z.string(),
          })
          .refine((data) => data?.xmlCode && data?.shortName && data?.name, {
            message: "Cargo Package type cannot be empty!",
            path: [],
          }),
        cargoPackageBLas: z.string().optional(),
        hsCode: z.string().optional(),
        scheduleBNumber: z.string().optional(),
        cargoDescription: z.string(),
        ncmCodes: z.string().optional(),
        markAndNumbers: z.string().optional(),
        cusCodes: z.string().optional(),
        cargoGrossWeight: z.string().nonempty("Cargo gross weight required!"),
        cargoGrossWeightUnit: z.string(),
        cargoGrossVolume: z.string().optional(),
        cargoGrossVolumeUnit: z.string().optional(),
      })
    ),

    // Control totals.
    totalNumberOfContainers: z.string().optional(),
    totalNumberOfPackages: z.string().optional(),
    totalShipmentWeight: z.string(),
    totalShipmentVolume: z.string(),

    // Shippers Declared value.
    currencyType: z
      .object({
        currencyCode: z.string(),
        currencyName: z.string(),
        currencyType: z.string(),
        name: z.string(),
      })
      .optional(),
    shippersDeclaredValue: z.string().optional(),

    // Freight Charges.
    typeOfFreightCharge: z.string().optional(),
    freightTerm: z.string().optional(),
    payer: z.string().optional(),
    paymentLocation: z
      .object({
        name: z.string(),
        location: z.string(),
        locode: z.string(),
      })
      .optional(),
    individualCharges: z.array(
      z.object({
        chargeType: z.string().optional(),
        freightTerm: z.string().optional(),
        payer: z.string().optional(),
        paymentLocation: z
          .object({
            name: z.string(),
            location: z.string(),
            locode: z.string(),
          })
          .optional(),
      })
    ),
    // Documentation.
    clauses: z.array(z.string()),
    userDefinedClauses: z.string().optional(),
    BLReleaseOffice: z
      .object({
        name: z.string(),
        location: z.string(),
        locode: z.string(),
      })
      .optional(),
    printBLReleaseOffice: z.string().optional(),
    requestedDateOfIssue: z.string().optional(),

    // BL Print Instructions.
    blPrintingInstructionType: z.enum(["express", "original", "houseBill"]).optional(),
    freighted: twoDigitOptional,
    unfreighted: twoDigitOptional,
    nonNegotiableFreighted: twoDigitOptional,
    nonNegotiableUnfreighted: twoDigitOptional,
    houseBillNumber: z.string().optional(),
    blComments: z.string().optional(),

    // Notificarions.
    sIRequestorEmails: commaSeparatedEmails.optional(),
    partnerNotificationEmails: commaSeparatedEmails.optional(),
  })
  .superRefine((data, ctx) => {
    const {
      typeOfFreightCharge,
      payer,
      freightTerm,
      paymentLocation,
      individualCharges,
      isShippingSingleCargoOnly,
      particularContainers,
    } = data;

    // Case 1: All Charges
    if (typeOfFreightCharge === "allCharges") {
      if (!freightTerm || freightTerm.trim() === "") {
        ctx.addIssue({
          path: ["freightTerm"],
          code: z.ZodIssueCode.custom,
          message: "Freight term is required.",
        });
      }
      if (!payer || payer.trim() === "") {
        ctx.addIssue({
          path: ["payer"],
          code: z.ZodIssueCode.custom,
          message: "Payer is required.",
        });
      }
      // If payer is "payelsewhere", payment location is mandatory
      if (freightTerm === "Payable Elsewhere") {
        if (!paymentLocation || !paymentLocation.name || !paymentLocation.location || !paymentLocation.locode) {
          ctx.addIssue({
            path: ["paymentLocation"],
            code: z.ZodIssueCode.custom,
            message: "Payment location is required when freightTerm is 'Payable Elsewhere'.",
          });
        }
      }
    }
    // Case 2: Individual Charges
    if (typeOfFreightCharge === "individualCharges") {
      // Check if at least one charge exists
      if (!individualCharges || individualCharges.length === 0) {
        ctx.addIssue({
          path: ["individualCharges"],
          code: z.ZodIssueCode.custom,
          message: "At least one individual charge is required.",
        });
      } else {
        // Check if at least one charge has "Basic freight" as chargeType
        const hasBasicFreight = individualCharges.some((charge) => charge.chargeType == "Basic Freight");

        if (!hasBasicFreight) {
          ctx.addIssue({
            path: ["individualCharges"],
            code: z.ZodIssueCode.custom,
            message: "At least one charge must have 'Basic freight' as charge type.",
          });
        }

        // Validate each individual charge
        individualCharges.forEach((charge, index) => {
          if (!charge.chargeType || charge.chargeType.trim() === "") {
            ctx.addIssue({
              path: ["individualCharges", index, "chargeType"],
              code: z.ZodIssueCode.custom,
              message: "Charge type is required.",
            });
          }
          if (!charge.freightTerm || charge.freightTerm.trim() === "") {
            ctx.addIssue({
              path: ["individualCharges", index, "freightTerm"],
              code: z.ZodIssueCode.custom,
              message: "Freight term is required.",
            });
          }
          if (!charge.payer || charge.payer.trim() === "") {
            ctx.addIssue({
              path: ["individualCharges", index, "payer"],
              code: z.ZodIssueCode.custom,
              message: "Payer is required.",
            });
          }

          // If freightTerm is "payelsewhere", payment location is mandatory for this charge
          if (charge.freightTerm === "Payable Elsewhere") {
            if (
              !charge.paymentLocation ||
              !charge.paymentLocation.name ||
              !charge.paymentLocation.location ||
              !charge.paymentLocation.locode
            ) {
              ctx.addIssue({
                path: ["individualCharges", index, "paymentLocation"],
                code: z.ZodIssueCode.custom,
                message: "Payment location is required when payer is 'Payable Elsewhere'.",
              });
            }
          }
        });
      }
    }

    if (isShippingSingleCargoOnly) {
      const first = particularContainers[0];
      if (!first?.hsCode || first.hsCode.trim() === "") {
        ctx.addIssue({
          path: ["particularContainers", 0, "hsCode"],
          code: z.ZodIssueCode.custom,
          message: "HS Code is required.",
        });
      }
      if (!first?.cargoDescription || first.cargoDescription.trim() === "") {
        ctx.addIssue({
          path: ["particularContainers", 0, "cargoDescription"],
          code: z.ZodIssueCode.custom,
          message: "Cargo Descrition is required.",
        });
      }
    } else {
      particularContainers.forEach((container, index) => {
        if (!container.cargoDescription || container.cargoDescription.trim() === "") {
          ctx.addIssue({
            path: ["particularContainers", index, "cargoDescription"],
            code: z.ZodIssueCode.custom,
            message: "Cargo Descrition is required.",
          });
        }
      });
    }
    const containerNumberMap = new Map<string, number[]>();
    particularContainers.forEach((c, idx) => {
      const num = c.containerNumber?.trim()?.toUpperCase();
      if (num) {
        if (!containerNumberMap.has(num)) {
          containerNumberMap.set(num, [idx]);
        } else {
          containerNumberMap.get(num)?.push(idx);
        }
      }
    });
    // Add issue for each duplicate index
    containerNumberMap.forEach((indexes) => {
      if (indexes.length > 1) {
        indexes.forEach((i) => {
          ctx.addIssue({
            path: ["particularContainers", i, "containerNumber"],
            code: z.ZodIssueCode.custom,
            message: "Duplicate container number is not allowed.",
          });
        });
      }
    });

    // 🚫 Duplicate seal number check (carrierSealNumber + shipperSealNumber combined)
    const sealMap = new Map<string, { indexes: number[]; field: string }[]>();

    particularContainers.forEach((c, idx) => {
      // We'll check for both carrierSealNumber and shipperSealNumber
      ["carrierSealNumber", "shipperSealNumber"].forEach((sealField) => {
        const sealVal = (c as any)[sealField];
        if (sealVal && sealVal.trim() !== "") {
          // Split into individual seals
          const seals = sealVal
            .split(",")
            .map((p: string) => p.trim().toUpperCase())
            .filter(Boolean);
          seals.forEach((seal: any) => {
            const key = `${seal}`;
            if (!sealMap.has(key)) {
              sealMap.set(key, [{ indexes: [idx], field: sealField }]);
            } else {
              const existing = sealMap.get(key)!;
              const foundFieldEntry = existing.find((e) => e.field === sealField);
              if (foundFieldEntry) {
                foundFieldEntry.indexes.push(idx);
              } else {
                existing.push({ indexes: [idx], field: sealField });
              }
            }
          });
        }
      });
    });

    // Add issue for duplicates
    sealMap.forEach((entries, sealKey) => {
      entries.forEach(({ indexes, field }) => {
        if (indexes.length > 1) {
          indexes.forEach((i) => {
            ctx.addIssue({
              path: ["particularContainers", i, field],
              code: z.ZodIssueCode.custom,
              message: `Duplicate seal number "${sealKey}" is not allowed.`,
            });
          });
        }
      });
    });
  });

export type SIRequestFormType = z.infer<typeof SIRequestFormSchema>;

export const SIRequestDefaultValues: SIRequestFormType = {
  bookingNumber: "",
  creationDate: "",
  bookingRequestId: "",

  // top parties general details.
  shipper: "",
  customShipperName: "",
  shipperEmail: "",
  shipperMobileNumber: "",
  shipperAddress: "",
  forwarder: "",
  forwarderName: "",
  forwarderAddress: "",

  // bottom partier general details.
  consignee: "",
  consigneeName: "",
  consigneeAddress: "",
  notifyParty: "",
  notifyPartyName: "",
  notifyPartyAddress: "",

  // references.
  shipperReferenceNumber: "",
  forwarderReferenceNumber: "",
  transactionReferenceNumber: "",
  uniqueConsignmentReferenceNumber: "",
  purchaseOrderNumber: "",
  contractReferenceNumber: "",
  BLReferenceNumber: "",
  exportersReferenceNumbers: "",
  consigneeOrderNumber: "",
  invoiceReferenceNumber: "",
  letterOfCreditReferenceNumber: "",
  letterOfCreditIssueDate: "",
  letterOfCreditExpiryDate: "",
  customsHouseBrokerReferenceNumber: "",
  govtReferenceOrFMCNumber: "",
  exportLicenseNumber: "",
  exportLicenseIssueDate: "",
  exportLicenseExpiryDate: "",

  // carrier Tab section.
  carrier: "",
  carrierBookingNumber: "",
  UCACarrier: "",
  UCAEmail: "",
  UCACarrierBookingNumber: "",

  // additional Parties.
  additionalNotifyParty1: "",
  additionalNotifyParty1Name: "",
  additionalNotifyParty1Address: "",
  additionalNotifyParty2: "",
  additionalNotifyParty2Name: "",
  additionalNotifyParty2Address: "",
  contractParty: "",
  contractPartyName: "",
  contractPartyAddress: "",
  freightPayer: "",
  freightPayerAddress: "",
  supplier: "",
  supplierAddress: "",
  consolidator: "",
  consolidatorAddress: "",
  importer: "",
  importerAddress: "",
  warehouseKeeper: "",
  warehouseKeeperAddress: "",

  // transport.
  vessel: "",
  voyage: "",
  IMONumber: "",
  placeOfCarrierReceipt: {
    name: "",
    location: "",
    locode: "",
  },
  placeOfCarrierReceiptBLas: "",
  portOfLoad: {
    name: "",
    location: "",
    locode: "",
  },
  portOfLoadBLas: "",
  portOfDischarge: {
    name: "",
    location: "",
    locode: "",
  },
  portOfDischargeBLas: "",
  placeOfCarrierDelivery: {
    name: "",
    location: "",
    locode: "",
  },
  placeOfCarrierDeliveryBLas: "",
  moveType: "",
  shipmentType: "",

  // Customs Compliance.
  shipperTaxId: "",
  consigneeTaxId: "",
  notifyPartyTaxId: "",
  printTaxIdOnBillOfLaiding: "1",

  // ICS2 Summary Declaration.
  goodsDeliveriedCountry: "",
  typeOfDeclaration: "",
  shipmentHouseBillStatus: "",
  pcin: "",
  csn: "",
  acidNumber: "",
  // House bill modal
  housebillOfLadingNumber: "",
  placeOfAcceptance: {
    name: "",
    location: "",
    locode: "",
  },
  placeOfFinalDelivery: {
    name: "",
    location: "",
    locode: "",
  },
  firstCountry: "",
  countriesVisitedInBetween: [],
  lastCountry: "",
  methodOfPayment: "",
  shipperPartyInvolved: {
    actualPartyInformationSameAs: "",
    partyId: "",
    actualParty: "",
    streetNumber: "",
    poBOX: "",
    streetName: "",
    state: "",
    city: "",
    country: "",
    postalCode: "",
    taxID: "",
    eori: "",
  },
  consigneePartyInvolved: {
    actualPartyInformationSameAs: "",
    partyId: "",
    actualParty: "",
    streetNumber: "",
    poBOX: "",
    streetName: "",
    state: "",
    city: "",
    country: "",
    postalCode: "",
    taxID: "",
    eori: "",
  },
  notifyPartyInvolved: {
    actualPartyInformationSameAs: "",
    partyId: "",
    actualParty: "",
    streetNumber: "",
    poBOX: "",
    streetName: "",
    state: "",
    city: "",
    country: "",
    postalCode: "",
    taxID: "",
    eori: "",
  },

  // particulars.
  isShippingSingleCargoOnly: true,
  particularContainers: [
    {
      // container details.
      containerNumber: "",
      containerType: {
        name: "",
        type: "",
      },
      containerSupplier: "",
      containerTareWeight: {
        weight: "",
        unit: "KG",
      },
      woodDeclaration: "",
      carrierSealNumber: "",
      shipperSealNumber: "",
      containerTerminalOperatorSealNumber: "",
      containerVeterinarySealNumber: "",
      containerCustomsSealNumber: "",
      containerCustomsReleaseCode: "",
      containerStuffingLocation: "",
      containerComments: "",
      // cargo details.
      cargoPackageCount: "",
      cargoPackageType: {
        xmlCode: "",
        shortName: "",
        name: "",
      },
      cargoPackageBLas: "",
      hsCode: "",
      scheduleBNumber: "",
      cargoDescription: "",
      ncmCodes: "",
      markAndNumbers: "",
      cusCodes: "",
      cargoGrossWeight: "",
      cargoGrossWeightUnit: "KG",
      cargoGrossVolume: "",
      cargoGrossVolumeUnit: "CBM",
    },
  ],

  // Control totals.
  totalNumberOfContainers: "",
  totalNumberOfPackages: "",
  totalShipmentWeight: "",
  totalShipmentVolume: "",

  // Shippers Declared value.
  currencyType: {
    currencyCode: "",
    currencyType: "",
    currencyName: "",
    name: "",
  },
  shippersDeclaredValue: "",

  // Freight Charges.
  typeOfFreightCharge: "allCharges",
  freightTerm: "",
  payer: "",
  paymentLocation: {
    name: "",
    location: "",
    locode: "",
  },
  individualCharges: [
    {
      chargeType: "Basic Freight",
      freightTerm: "",
      payer: "",
      paymentLocation: {
        name: "",
        location: "",
        locode: "",
      },
    },
  ],

  // Documentation.
  clauses: [""],
  userDefinedClauses: "",
  BLReleaseOffice: {
    name: "",
    location: "",
    locode: "",
  },
  printBLReleaseOffice: "",
  requestedDateOfIssue: "",

  // BL Print Instructions.
  blPrintingInstructionType: "express",
  freighted: "",
  unfreighted: "",
  nonNegotiableFreighted: "",
  nonNegotiableUnfreighted: "",
  houseBillNumber: "",
  blComments: "",

  // Notificarions.
  sIRequestorEmails: "",
  partnerNotificationEmails: "",
};
