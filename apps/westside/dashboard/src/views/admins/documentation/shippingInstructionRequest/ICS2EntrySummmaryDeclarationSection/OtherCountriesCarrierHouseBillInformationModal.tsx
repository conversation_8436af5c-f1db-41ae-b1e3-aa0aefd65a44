import SpinnerLoader from "@/components/Loader/SpinnerLoader";
import { Typography } from "@/components/typography";
import { <PERSON><PERSON>, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { COUNTRYLISTISODATA } from "@/constants/countries";
import { fetchBookingLocations } from "@/services/admin/booking";
import { BookingRequestGeneralType } from "@/types/booking";
import { useQuery } from "@tanstack/react-query";
import { AlertCircleIcon, Check, ChevronsUpDown, Info, X } from "lucide-react";
import { FC, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import ICS2Lookup from "./ICS2Lookup";
import { toast } from "sonner";
import MultiSelectCountries from "@/components/shippingInstruction/MultiSelectCountries";
interface OtherCountriesCarrierHouseBillInformationModalProps {
  initialData?: BookingRequestGeneralType["data"];
}

const OtherCountriesCarrierHouseBillInformationModal: FC<OtherCountriesCarrierHouseBillInformationModalProps> = ({
  initialData,
}) => {
  const { control, setValue, watch } = useFormContext();

  console.log(watch("countriesVisitedInBetween"));

  type PartyType = "Shipper" | "Consignee" | "Notify Party";

  const parties: PartyType[] = ["Shipper", "Consignee", "Notify Party"];

  const handleClearAllData = () => {
    setValue("housebillOfLadingNumber", "");
    setValue("placeOfAcceptance", { name: "", location: "", locode: "" });
    setValue("placeOfFinalDelivery", { name: "", location: "", locode: "" });
    setValue("firstCountry", "");
    setValue("countriesVisitedInBetween", "");
    setValue("lastCountry", "");
    setValue("methodOfPayment", "");

    setValue("shipperPartyInvolved", {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    });

    setValue("consigneePartyInvolved", {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    });

    setValue("notifyPartyInvolved", {
      actualPartyInformationSameAs: "",
      partyId: "",
      actualParty: "",
      streetNumber: "",
      poBOX: "",
      streetName: "",
      state: "",
      city: "",
      country: "",
      postalCode: "",
      taxID: "",
      eori: "",
    });
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button type="button" variant={"ghost"} className="px-0 text-primary">
          Add House Bill Information
        </Button>
      </SheetTrigger>
      <SheetContent
        removeCancel
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
        className="w-9/12"
      >
        <SheetHeader>
          <SheetTitle>House Bill Information For Customs Filing</SheetTitle>
          {/* <SheetDescription>
            {" "}
            Please provide the complete house bill information else carrier may reject the SI and the containers may not
            be loaded on the vessel.{" "}
          </SheetDescription> */}
        </SheetHeader>
        <ScrollArea className="h-10/12 ">
          <div className="px-4 pb-4">
            <Alert variant="default" className="bg-amber-200/20">
              <AlertCircleIcon />
              <AlertTitle>
                {" "}
                Please provide the complete house bill information else carrier may reject the SI and the containers may
                not be loaded on the vessel.{" "}
              </AlertTitle>
            </Alert>
          </div>
          <div className="px-4 flex flex-col gap-5">
            <hr />
            <div className="">
              <FormField
                control={control}
                name="housebillOfLadingNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-secondary">
                      <div className="flex items-center gap-1 ">
                        <span className="text-secondary"> House Bill of Lading Number </span>
                        <span className="">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Info size={15} />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-72">
                                <Typography variant={"small"} className="text-white">
                                  Enter House Bill Number or a Unique Id
                                </Typography>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </span>
                      </div>
                    </FormLabel>
                    <FormControl className="">
                      <Input type="text" placeholder="Enter Number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Typography variant={"h5"} weight={"semibold"}>
              Origin & Destination
            </Typography>
            <hr />
            <div className="grid grid-cols-3 gap-5">
              <FormField
                control={control}
                name="placeOfAcceptance"
                render={({ field }) => {
                  const { value, onChange } = field;
                  const [query, setQuery] = useState<string>("");
                  const [open, setOpen] = useState(false);
                  const [shownValue, setShownValue] = useState<string>(value?.location || "");
                  useEffect(() => {
                    setShownValue(value?.location);
                  }, [value]);

                  const {
                    data: locationData,
                    error: locationError,
                    isFetching: locationFetching,
                  } = useQuery({
                    queryKey: [
                      "fetchBookingLocation",
                      {
                        search: query,
                      },
                    ],
                    queryFn: fetchBookingLocations,
                  });

                  const handleSearch = (val: string) => {
                    setQuery(val);
                  };

                  return (
                    <FormItem>
                      <FormLabel className="flex items-center justify-between">
                        <span className="">Place of Acceptance (Origin of Goods)</span>
                        {value?.name || value?.location || value?.locode ? (
                          <Typography
                            onClick={() => {
                              setValue("placeOfAcceptance", {
                                name: "",
                                location: "",
                                locode: "",
                              });
                              setValue("firstCountry", "");
                            }}
                            variant={"muted"}
                            className="underline cursor-pointer"
                          >
                            Clear
                          </Typography>
                        ) : (
                          ""
                        )}
                      </FormLabel>
                      <FormControl>
                        <Popover open={open} onOpenChange={setOpen}>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={open}
                              className="justify-between w-full overflow-hidden h-11"
                            >
                              {value?.name ? shownValue : "Select Location..."}
                              <ChevronsUpDown className="opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder="Search Loction..."
                                className="h-9"
                                value={query}
                                onValueChange={(val) => handleSearch(val)}
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {locationFetching ? (
                                    <div className="flex justify-center w-full">
                                      <SpinnerLoader />
                                    </div>
                                  ) : (
                                    "  No Location Found."
                                  )}
                                </CommandEmpty>
                                <CommandGroup>
                                  {locationData?.message?.results?.length
                                    ? locationData?.message?.results?.map((location) => (
                                        <CommandItem
                                          key={location.name}
                                          value={`${location?.location_name}, ${
                                            location?.sub_division ? `${location?.sub_division},` : ""
                                          } ${location?.country} (${location?.locode})`}
                                          onSelect={() => {
                                            onChange({
                                              name: String(location.name),
                                              location: `${location?.location_name}, ${
                                                location?.sub_division ? `${location?.sub_division},` : ""
                                              } ${location?.country} (${location?.locode})`,
                                              locode: location?.locode,
                                            });
                                            setValue("firstCountry", location?.country);
                                            setShownValue(
                                              `${location?.location_name}, ${
                                                location?.sub_division ? `${location?.sub_division},` : ""
                                              } ${location?.country} (${location?.locode})`
                                            );

                                            setOpen(false);
                                          }}
                                        >
                                          {`${location?.location_name}, ${
                                            location?.sub_division ? `${location?.sub_division},` : ""
                                          } ${location?.country} (${location?.locode})`}
                                          <Check
                                            className={
                                              value?.name === String(location.name)
                                                ? "ml-auto opacity-100"
                                                : "ml-auto opacity-0"
                                            }
                                          />
                                        </CommandItem>
                                      ))
                                    : ""}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />

              <FormField
                control={control}
                name="placeOfFinalDelivery"
                render={({ field }) => {
                  const { value, onChange } = field;
                  const [query, setQuery] = useState<string>("");
                  const [open, setOpen] = useState(false);
                  const [shownValue, setShownValue] = useState<string>(value?.location || "");
                  useEffect(() => {
                    setShownValue(value?.location);
                  }, [value]);

                  const {
                    data: locationData,
                    error: locationError,
                    isFetching: locationFetching,
                  } = useQuery({
                    queryKey: [
                      "fetchBookingLocation",
                      {
                        search: query,
                      },
                    ],
                    queryFn: fetchBookingLocations,
                  });

                  const handleSearch = (val: string) => {
                    setQuery(val);
                  };

                  return (
                    <FormItem>
                      <FormLabel className="flex items-center justify-between">
                        <span className="">Place of Final Delivery</span>
                        {value?.name || value?.location || value?.locode ? (
                          <Typography
                            onClick={() => {
                              setValue("placeOfFinalDelivery", {
                                name: "",
                                location: "",
                                locode: "",
                              });
                              setValue("lastCountry", "");
                            }}
                            variant={"muted"}
                            className="underline cursor-pointer"
                          >
                            Clear
                          </Typography>
                        ) : (
                          ""
                        )}
                      </FormLabel>
                      <FormControl>
                        <Popover open={open} onOpenChange={setOpen}>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={open}
                              className="justify-between w-full overflow-hidden h-11"
                            >
                              {value?.name ? shownValue : "Select Location..."}
                              <ChevronsUpDown className="opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder="Search Loction..."
                                className="h-9"
                                value={query}
                                onValueChange={(val) => handleSearch(val)}
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {locationFetching ? (
                                    <div className="flex justify-center w-full">
                                      <SpinnerLoader />
                                    </div>
                                  ) : (
                                    "  No Location Found."
                                  )}
                                </CommandEmpty>
                                <CommandGroup>
                                  {locationData?.message?.results?.length
                                    ? locationData?.message?.results?.map((location) => (
                                        <CommandItem
                                          key={location.name}
                                          value={`${location?.location_name}, ${
                                            location?.sub_division ? `${location?.sub_division},` : ""
                                          } ${location?.country} (${location?.locode})`}
                                          onSelect={() => {
                                            onChange({
                                              name: String(location.name),
                                              location: `${location?.location_name}, ${
                                                location?.sub_division ? `${location?.sub_division},` : ""
                                              } ${location?.country} (${location?.locode})`,
                                              locode: location?.locode,
                                            });
                                            setValue("lastCountry", location?.country);
                                            setShownValue(
                                              `${location?.location_name}, ${
                                                location?.sub_division ? `${location?.sub_division},` : ""
                                              } ${location?.country} (${location?.locode})`
                                            );

                                            setOpen(false);
                                          }}
                                        >
                                          {`${location?.location_name}, ${
                                            location?.sub_division ? `${location?.sub_division},` : ""
                                          } ${location?.country} (${location?.locode})`}
                                          <Check
                                            className={
                                              value?.name === String(location.name)
                                                ? "ml-auto opacity-100"
                                                : "ml-auto opacity-0"
                                            }
                                          />
                                        </CommandItem>
                                      ))
                                    : ""}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>
            <Typography variant={"h5"} weight={"semibold"}>
              Countries of routing of consignment
            </Typography>
            <hr />
            <div className="grid grid-cols-3 gap-5">
              <FormField
                control={control}
                name="firstCountry"
                disabled
                render={({ field }) => (
                  <FormItem className="cursor-not-allowed">
                    <FormLabel className="text-secondary">First Country</FormLabel>
                    <FormControl className="w-full">
                      <Input aria-disabled disabled type="text" placeholder="Enter First country" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <MultiSelectCountries />
              <FormField
                control={control}
                name="lastCountry"
                disabled
                render={({ field }) => (
                  <FormItem className="cursor-not-allowed">
                    <FormLabel className="text-secondary">Last Country</FormLabel>
                    <FormControl className="w-full">
                      <Input aria-disabled disabled type="text" placeholder="Enter Last Country" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Typography variant={"h5"} weight={"semibold"}>
              Transportation charges / Method of payment
            </Typography>
            <hr />
            <div className="">
              <FormField
                control={control}
                name={"methodOfPayment"}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-secondary">Method of Payment</FormLabel>
                    <FormControl>
                      <Select value={field.value}>
                        <SelectTrigger className="w-full bg-white">
                          <SelectValue placeholder="Choose One" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={"None"}>Select One</SelectItem>
                          <SelectItem value={"Account holder with carrier"}>Account holder with carrier</SelectItem>
                          <SelectItem value={"Electronic funds transfer"}>Electronic funds transfer</SelectItem>
                          <SelectItem value={"Not pre-paid"}>Not pre-paid</SelectItem>
                          <SelectItem value={"Other"}>Other</SelectItem>
                          <SelectItem value={"Payment in cash"}>Payment in cash</SelectItem>
                          <SelectItem value={"Payment by cheque"}>Payment by cheque</SelectItem>
                          <SelectItem value={"Payment by credit card"}>Payment by credit card</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Typography variant={"h5"} weight={"semibold"}>
              Parties involved
            </Typography>
            <hr />
            <div className="grid grid-cols-3 gap-5 pb-20">
              {parties?.map((item, index) => {
                let party = "";
                switch (item) {
                  case "Shipper":
                    party = "shipperPartyInvolved";
                    break;
                  case "Consignee":
                    party = "consigneePartyInvolved";
                    break;
                  case "Notify Party":
                    party = "notifyPartyInvolved";
                    break;
                }
                return (
                  <div key={index} className="border rounded flex flex-col gap-5 px-4 py-6">
                    <FormField
                      control={control}
                      name={`${party}.actualPartyInformationSameAs`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-secondary">Actual {item} Information Same As</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(data) => {
                                setValue(party, {
                                  actualPartyInformationSameAs: "",
                                  partyId: "",
                                  actualParty: "",
                                  streetNumber: "",
                                  poBOX: "",
                                  streetName: "",
                                  state: "",
                                  city: "",
                                  country: "",
                                  postalCode: "",
                                  taxID: "",
                                  eori: "",
                                });
                                field.onChange(data);
                                switch (data) {
                                  case "Buyer":
                                    toast.error(
                                      "Structured Address information is not available. Enter the party information."
                                    );
                                    break;
                                  case "Seller":
                                    toast.error(
                                      "Structured Address information is not available. Enter the party information."
                                    );
                                    break;
                                  case "Shipper":
                                    if (watch("shipper")) {
                                      const shipper = initialData?.shippers?.find(
                                        (shpr) => shpr?.name === watch("shipper")
                                      );
                                      setValue(`${party}.partyId`, shipper?.name || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                      setValue(`${party}.actualParty`, shipper?.shipper_name || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                      setValue(`${party}.postalCode`, shipper?.postal_code || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                    } else {
                                      setValue(`${party}.actualParty`, watch("customShipperName") || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                      toast.error(
                                        "Structured Address information is not available. Enter the party information."
                                      );
                                    }
                                    break;
                                  case "Consignee":
                                    if (watch("consignee")) {
                                      const consignee = initialData?.customers?.find(
                                        (con) => con?.name === watch("consignee")
                                      );
                                      setValue(`${party}.partyId`, consignee?.name || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                      setValue(`${party}.actualParty`, consignee?.customer_name || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                      setValue(`${party}.state`, consignee?.customer_state || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                      setValue(`${party}.city`, consignee?.customer_city || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                      setValue(`${party}.country`, consignee?.customer_country || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                    } else {
                                      setValue(`${party}.actualParty`, watch("consigneeName") || "", {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                      });
                                      toast.error(
                                        "Structured Address information is not available. Enter the party information."
                                      );
                                    }
                                    break;
                                  case "Notify Party":
                                    if (watch("notifyParty")) {
                                      const customer = initialData?.customers?.find(
                                        (cus) => cus?.name === watch("notifyParty")
                                      );
                                      setValue(`${party}.partyId`, customer?.name || "");
                                      setValue(`${party}.actualParty`, customer?.customer_name || "");
                                      setValue(`${party}.state`, customer?.customer_state || "");
                                      setValue(`${party}.city`, customer?.customer_city || "");
                                      setValue(`${party}.country`, customer?.customer_country || "");
                                    } else {
                                      setValue(`${party}.actualParty`, watch("notifyPartyName") || "");
                                      toast.error(
                                        "Structured Address information is not available. Enter the party information."
                                      );
                                    }
                                    break;
                                }
                              }}
                              value={field.value}
                            >
                              <SelectTrigger className="w-full bg-white">
                                <SelectValue placeholder="Choose One" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value={"None"}>Select One</SelectItem>
                                <SelectItem value={"Buyer"}>Buyer</SelectItem>
                                <SelectItem value={"Consignee"}>Consignee</SelectItem>
                                <SelectItem value={"Notify Party"}>Notify Party</SelectItem>
                                <SelectItem value={"Seller"}>Seller</SelectItem>
                                <SelectItem value={"Shipper"}>Shipper</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={control}
                      name={`${party}.actualParty`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="">
                            {" "}
                            <div className="flex items-end justify-between w-full">
                              <p className="text-secondary">Actual {item}</p>
                              <div className="flex gap-1">
                                {field?.value ? (
                                  <Button
                                    onClick={() => {
                                      setValue(party, {
                                        actualPartyInformationSameAs: "",
                                        partyId: "",
                                        actualParty: "",
                                        streetNumber: "",
                                        poBOX: "",
                                        streetName: "",
                                        state: "",
                                        city: "",
                                        country: "",
                                        postalCode: "",
                                        taxID: "",
                                        eori: "",
                                      });
                                    }}
                                    type="button"
                                    variant={"secondary"}
                                    className="text-white h-7"
                                  >
                                    <X />
                                  </Button>
                                ) : (
                                  ""
                                )}
                                <ICS2Lookup
                                  shippers={initialData?.shippers}
                                  customers={initialData?.customers}
                                  inputName={item}
                                />
                              </div>
                            </div>
                          </FormLabel>
                          <FormLabel className="text-secondary"> </FormLabel>
                          <FormControl>
                            <Textarea {...field} value={field.value} placeholder="Enter Name" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="grid grid-cols-12">
                      <div className="col-span-5">
                        <FormField
                          control={control}
                          name={`${party}.streetNumber`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-secondary">Street Number</FormLabel>
                              <FormControl className="w-full">
                                <Input type="text" placeholder="Enter Street Number" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="col-span-2 flex items-end pb-2 justify-center">OR</div>
                      <div className="col-span-5">
                        <FormField
                          control={control}
                          name={`${party}.poBOX`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-secondary">P.O.BOX</FormLabel>
                              <FormControl className="w-full">
                                <Input type="text" placeholder="Enter P.O.BOX" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    <FormField
                      control={control}
                      name={`${party}.streetName`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-secondary">Street Name (35 X 2 lines) </FormLabel>
                          <FormControl>
                            <Textarea {...field} value={field.value} placeholder="Enter Street Name" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={control}
                      name={`${party}.contactName`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-secondary">Contact Name</FormLabel>
                          <FormControl>
                            <Input type="text" placeholder="Enter Contact Name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={control}
                      name={`${party}.state`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-secondary">State</FormLabel>
                          <FormControl className="w-full">
                            <Input type="text" placeholder="Enter State" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={control}
                      name={`${party}.city`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-secondary">City</FormLabel>
                          <FormControl className="w-full">
                            <Input type="text" placeholder="Enter City" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={control}
                      name={`${party}.country`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-secondary">Country</FormLabel>
                          <FormControl>
                            <Select value={field.value}>
                              <SelectTrigger className="w-full bg-white">
                                <SelectValue placeholder="Choose One" />
                              </SelectTrigger>
                              <SelectContent>
                                {COUNTRYLISTISODATA.map((item) => (
                                  <SelectItem key={item?.number} value={item?.code3}>
                                    {item?.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={control}
                      name={`${party}.postalCode`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-secondary">Postal Code</FormLabel>
                          <FormControl className="w-full">
                            <Input type="text" placeholder="Enter Postal Code" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={control}
                      name={`${party}.taxID`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-secondary">Tax ID</FormLabel>
                          <FormControl className="w-full">
                            <Input type="text" placeholder="Enter Tax ID" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={control}
                      name={`${party}.eori`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-secondary">EORI</FormLabel>
                          <FormControl className="w-full">
                            <Input type="text" placeholder="Enter EORI" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </ScrollArea>
        <SheetFooter className="">
          <div className="flex justify-end gap-5 ">
            <SheetClose asChild>
              <Button type="submit">Save changes</Button>
            </SheetClose>
            <SheetClose asChild>
              <Button onClick={() => handleClearAllData()} variant="outline">
                Clear & Close
              </Button>
            </SheetClose>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default OtherCountriesCarrierHouseBillInformationModal;
