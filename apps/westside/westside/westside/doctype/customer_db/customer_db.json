{"actions": [], "allow_rename": 1, "autoname": "CUS-.#####", "creation": "2025-01-27 12:02:01.601539", "doctype": "DocType", "engine": "InnoDB", "field_order": ["first_name", "last_name", "customer_name", "parent_company", "customer_city", "customer_state", "customer_zip", "customer_country", "column_break_pcub", "contact", "phone", "inttra_company_id", "customer_address", "email_id", "fax", "my_role", "user_id", "section_break_csrc", "tax_id1", "tax_id2", "tax_id3", "street_name", "street_number", "po_box", "column_break_bpfx", "license_attachments", "is_customs_applicable", "status", "is_active", "customer_free_form_address", "company_name", "notification_email", "flag_for_custom_docs", "quickbooks_customer_id"], "fields": [{"fieldname": "customer_name", "fieldtype": "Data", "in_list_view": 1, "label": "customer Name"}, {"fieldname": "parent_company", "fieldtype": "Data", "label": "Parent Company"}, {"fieldname": "customer_city", "fieldtype": "Data", "in_list_view": 1, "label": "Customer City"}, {"fieldname": "phone", "fieldtype": "Data", "in_list_view": 1, "label": "Phone"}, {"fieldname": "tax_id1", "fieldtype": "Data", "label": "Tax ID1(IEC)"}, {"fieldname": "tax_id2", "fieldtype": "Data", "label": "Tax ID2(GST)"}, {"fieldname": "column_break_pcub", "fieldtype": "Column Break"}, {"fieldname": "customer_state", "fieldtype": "Data", "in_list_view": 1, "label": "Customer State"}, {"fieldname": "customer_zip", "fieldtype": "Data", "label": "Customer Zip"}, {"fieldname": "customer_country", "fieldtype": "Link", "label": "Customer Country", "options": "Country"}, {"fieldname": "contact", "fieldtype": "Data", "label": "Contact"}, {"fieldname": "email_id", "fieldtype": "Data", "label": "Email ID"}, {"fieldname": "license_attachments", "fieldtype": "Table", "label": "License attachments", "options": "License attachments"}, {"fieldname": "section_break_csrc", "fieldtype": "Section Break"}, {"fieldname": "column_break_bpfx", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "is_customs_applicable", "fieldtype": "Check", "label": "Is Customs Applicable"}, {"fieldname": "fax", "fieldtype": "Data", "label": "Fax"}, {"fieldname": "inttra_company_id", "fieldtype": "Data", "label": "Inttra Company Id"}, {"default": "1", "fieldname": "is_active", "fieldtype": "Check", "label": "Is Active"}, {"fieldname": "first_name", "fieldtype": "Data", "label": "First Name"}, {"fieldname": "last_name", "fieldtype": "Data", "label": "Last Name"}, {"default": "Pending", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Pending\nApproved\nRejected"}, {"fieldname": "tax_id3", "fieldtype": "Data", "label": "Tax ID3(PAN)"}, {"fieldname": "customer_address", "fieldtype": "Small Text", "label": "Address"}, {"fieldname": "customer_free_form_address", "fieldtype": "Small Text", "label": "Customer Free Form Address"}, {"fieldname": "company_name", "fieldtype": "Data", "label": "Company Name", "reqd": 1, "unique": 1}, {"fieldname": "notification_email", "fieldtype": "Data", "label": "Notification email"}, {"default": "0", "fieldname": "flag_for_custom_docs", "fieldtype": "Check", "label": "Flag for Custom docs"}, {"default": "0", "fieldname": "my_role", "fieldtype": "Check", "label": "My Role"}, {"fieldname": "quickbooks_customer_id", "fieldtype": "Data", "label": "QuickBooks Customer ID"}, {"fieldname": "user_id", "fieldtype": "Link", "label": "User Id", "options": "User", "read_only": 1}, {"fieldname": "street_name", "fieldtype": "Data", "label": "Street Name"}, {"fieldname": "po_box", "fieldtype": "Data", "label": "PO Box"}, {"fieldname": "street_number", "fieldtype": "Data", "label": "Street No"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-08 11:55:09.106830", "modified_by": "Administrator", "module": "Westside", "name": "Customer DB", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "customer_name"}