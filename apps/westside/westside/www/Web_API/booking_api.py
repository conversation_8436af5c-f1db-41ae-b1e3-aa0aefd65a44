
import frappe


@frappe.whitelist(allow_guest=True)
def fetch_booking_data():
    try:
        
        carriers = frappe.get_all(
            "Carrier", fields=["partyalias", "partyname1", "name", "inttra_id", "address", "postal_code", "country_code", ])

        shippers = frappe.get_all("Shipper", fields=["name", "shipper_name", "contact_name", "shipper_details", "shipper_code", "inttra_company_id", "email", "phone", "custom_address", "subscription", "postal_code", "my_role"])

        container_types = frappe.get_all("Container Type", fields=[
                                         "name","typecode", "shortdescription","typecategory", "groupcode", "description", "listheight", "oogflag", "height", "listlength", "length", "listwidth", "width", "spareindicator", "nonstandardtype", "displayflag", "displaycategory"])

        customers = frappe.get_all("Customer DB", filters={"is_active": 1}, fields=[
                                   "name","customer_name","first_name", "last_name", "inttra_company_id","parent_company","customer_free_form_address as customer_address", "customer_zip", "customer_city", "customer_state", "company_name", "customer_country", "email_id", "phone","fax","street_name", "street_number", "po_box","my_role"])

        notify_party = frappe.get_all("Notify Party", fields=[
                                      "name","name1", "phone", "email", "fax", "postal_code",  "country", "address","my_role"])

        forwarder = frappe.get_all("Forwarder", fields=[
                                   "name","contact_name", "inttra_company_id", "contact_number", "postal_code", "address", "email", "fax", "country","my_role"])

        contract_party = frappe.get_all("Contract Party", fields=[
                                        "name","name1", "phone", "email", "fax", "postal_code", "address", "country", "address", "inttra_company_id","my_role"])
        
        additional_notify_party_1 = frappe.get_all("Additional Notify Party 1", fields=[
                                        "name","name1", "phone", "email", "fax", "postal_code", "address", "country", "address", "inttra_company_id","my_role"])
        
        additional_notify_party_2 = frappe.get_all("Additional Notify Party 2", fields=[
                                        "name","name1", "phone", "email", "fax", "postal_code", "address", "country", "address", "inttra_company_id","my_role"])
        
        customs_broker = frappe.get_all("Customs Broker", fields=[
                                        "name","name1", "phone", "email", "fax", "postal_code", "address", "country", "address", "inttra_company_id","my_role"])
        

        return {
            "status": "success",
            "data": {
                "carriers": carriers,
                "shippers": shippers,
                "container_types": container_types,
                "customers": customers,
                "notify_party": notify_party,
                "forwarder": forwarder,
                "contract_party": contract_party,
                "additional_notify_party_1": additional_notify_party_1,
                "additional_notify_party_2": additional_notify_party_2,
                "customs_broker": customs_broker
            }
        }
    except Exception as e:
        frappe.log_error(
            f"Error fetching booking data: {str(e)}", "Booking API Error")
        return {
            "status": "error",
            "message": str(e)
        }


@frappe.whitelist(allow_guest=True)
def fetch_carrier_data():
    try:

        carriers = frappe.get_all(
            "Carrier", fields=["partyalias", "partyname1", "name"])

        return {
            "status": "success",
            "carriers": carriers,

        }
    except Exception as e:
        frappe.log_error(
            f"Error fetching booking data: {str(e)}", "Booking API Error")
        return {
            "status": "error",
            "message": str(e)
        }
