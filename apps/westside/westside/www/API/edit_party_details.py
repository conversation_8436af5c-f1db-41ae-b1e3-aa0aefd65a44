import frappe
import json
from frappe import _
from frappe.utils.response import build_response

@frappe.whitelist(allow_guest=True)
def create_customer_details():
    frappe.local.response.http_status_code = 200  

    try:
        data = frappe.local.form_dict
        if frappe.request and frappe.request.data:
            data = json.loads(frappe.request.data)

        customer_name = data.get("customer_name")
        customer_zip = data.get("customer_zip")
        phone = data.get("phone")
        customer_address = data.get("customer_address")
        email_id = data.get("email_id")
        fax = data.get("fax")
        company_name = data.get("company_name")

        if not customer_name:
            frappe.local.response.http_status_code = 400
            return {
                "status": "error",
                "status_code":400,
                "message": _("Missing required field: customer_name")
            }

        if frappe.db.exists("Customer DB", {"customer_name": customer_name}):
            frappe.local.response.http_status_code = 409  
            return {
                "status": "error",
                "status_code":409,
                "message": _(f"Customer with name '{customer_name}' already exists.")
            }

        new_customer = frappe.get_doc({
            "doctype": "Customer DB",
            "customer_name": customer_name,
            "customer_zip": customer_zip,
            "phone": phone,
            "customer_address": customer_address,
            "email_id": email_id,
            "fax": fax,
            "company_name":company_name
        })
        new_customer.insert(ignore_permissions=True)
        frappe.db.commit()

        return {
            "status": "success",
            "status_code": 200,
            "message": _(f"Customer '{customer_name}' created successfully."),
            "name": new_customer.name,
            "customer_name": new_customer.customer_name,
            "company_name": new_customer.company_name,
            "customer_address": new_customer.customer_address,
            "customer_zip": new_customer.customer_zip,
            "phone": new_customer.phone,
            "email_id": new_customer.email_id,
            "fax": new_customer.fax
        }

    except frappe.ValidationError as ve:
        frappe.local.response.http_status_code = 400
        return {
            "status": "error",
            "message": _(f"Validation failed: {str(ve)}")
        }

    except frappe.PermissionError:
        frappe.local.response.http_status_code = 403
        return {
            "status": "error",
            "message": _("You do not have permission to create a customer.")
        }

    except Exception as e:
        frappe.local.response.http_status_code = 500
        frappe.log_error(frappe.get_traceback(), "Customer DB Creation Error")
        return {
            "status": "error",
            "message": _("An unexpected error occurred while creating the customer.")
        }



@frappe.whitelist(allow_guest=True)
def update_customer_details(name):
    """
    Update an existing Customer record using primary key.

    Args:
        name (str): Primary key (Customer name/id) passed in URL params.
        JSON body:
            {
                "customer_name": "New Name",
                "customer_zip": "123456",
                "phone": "1234567890",
                "customer_address": "New Address",
                "email_id": "<EMAIL>",
                "fax": "123-456"
            }

    Returns:
        dict: status message and updated customer name or error details.
    """
    frappe.local.response.http_status_code = 200  

    if not name:
        frappe.local.response.http_status_code = 400
        return {"error": "Missing required parameter: name (primary key)."}

    try:
        if frappe.request and frappe.request.data:
            data = json.loads(frappe.request.data)
        else:
            frappe.local.response.http_status_code = 400
            return {"error": "Missing request body or invalid JSON."}

        
        customer = frappe.get_doc("Customer DB", name)

        
        fields_to_update = ["customer_name", "customer_zip", "phone", "customer_address", "email_id", "fax", "customer_free_form_address","street_name","street_number","po_box"]

        for field in fields_to_update:
            if field in data:
                setattr(customer, field, data[field])

        if "customer_free_form_address" in data:
            customer.customer_address = data["customer_free_form_address"]
        
        if "customer_address" in data:
            customer.customer_free_form_address = data["customer_address"]

        if "customer_name" in data:
            customer.company_name = data["customer_name"]

        customer.save(ignore_permissions=True)
        frappe.db.commit()

        return {
            "status": "success",
            "status_code": 200,
            "message": "Customer updated successfully.",
            "customer": customer.customer_name,
            "company_name": customer.company_name,
            "customer_address": customer.customer_address,
            "customer_free_form_address": customer.customer_free_form_address,
            "customer_zip": customer.customer_zip,
            "phone": customer.phone,
            "email_id": customer.email_id,
            "fax": customer.fax
        }

    except frappe.DoesNotExistError:
        frappe.local.response.http_status_code = 404
        return {"error": f"Customer with name '{name}' not found."}

    except Exception as e:
        frappe.log_error(title="Update Customer Error", message=frappe.get_traceback())
        frappe.local.response.http_status_code = 500
        return {"error": "An unexpected error occurred.", "details": str(e)}
