import frappe
from frappe import _
import json
from frappe.utils.file_manager import save_file
from westside.www.Authentication.auth_decorators import role_required




@frappe.whitelist()
@role_required(["Admin"])
def create_customer():
    try:
        data_str = frappe.form_dict.get("data")
        license_attachments = frappe.request.files.getlist("license_attachments")
        
        try:
            data = json.loads(data_str)
        except json.JSONDecodeError as e:
            return { "status_code": 500, "message": f"Invalid JSON format: {str(e)}"}

        first_name = data.get("first_name")
        last_name = data.get("last_name")
        customer_name = data.get("company_name")  # currently customer name is pointed to company_name
        company_name = data.get("company_name")
        parent_company = data.get("parent_company")
        contact = data.get("contact")
        phone = data.get("phone")
        email_id = data.get("email_id")
        customer_zip = data.get("customer_zip")
        customer_city = data.get("customer_city")
        customer_state = data.get("customer_state")
        customer_country = data.get("customer_country")
        tax_id1 = data.get("tax_id1")
        tax_id2 = data.get("tax_id2")
        tax_id3 = data.get("tax_id3") 
        customer_free_form_address = data.get("customer_free_form_address")
        notification_email = data.get("notification_email")
        flag_for_custom_docs = data.get("flag_for_custom_docs")
        street_name = data.get("street_name")
        street_number = data.get("street_number")
        po_box = data.get("po_box")

        # Check if company_name already exists (if not null)
        if company_name:
            if frappe.db.exists("Customer DB", {"company_name": company_name}):
                return {
                    "status_code": 409,
                    "message": f"Customer with company name '{company_name}' already exists."
                }

        # Validate customer_country (code)
        country_name = ""
        if customer_country:
            country_doc = frappe.db.get_value("Country", {"code": customer_country.lower()})
            if not country_doc:
                return {
                    "status_code": 404,
                    "message": f"Invalid country code: '{customer_country.lower()}'."
                }
            country_name = country_doc 
            
        # create customer doc
        customer = frappe.get_doc({
            "doctype": "Customer DB",
            "first_name": first_name,
            "last_name": last_name,
            "customer_name": customer_name,
            "company_name": company_name,
            "parent_company": parent_company,
            "phone": phone,
            "contact": contact,
            "email_id": email_id,
            "customer_zip": customer_zip,
            "customer_city": customer_city,
            "customer_state": customer_state,
            "customer_country": country_name,
            "tax_id1": tax_id1,
            "tax_id2": tax_id2,
            "tax_id3": tax_id3,
            "customer_address": customer_free_form_address,
            "customer_free_form_address": customer_free_form_address,
            "notification_email" : notification_email,
            "flag_for_custom_docs" : flag_for_custom_docs,
            "street_name": street_name,
            "street_number": street_number,
            "po_box": po_box
        })

        customer.insert(ignore_permissions=True)

        # Upload license attachments
        if license_attachments:
            site_url = frappe.utils.get_url()

            for license_file in license_attachments:
                
                # Save file to File doctype and attach to parent Job
                file_doc = save_file(
                    license_file.filename,
                    license_file.stream.read(),
                    "Customer DB", 
                    customer.name, 
                    is_private=0
                )

                # Add reference to the file in the child table
                customer.append("license_attachments", {
                    "license": site_url + file_doc.file_url,
                    "file_name": license_file.filename
                })
        
        customer.save()
        frappe.db.commit()

        return {
            "status_code": 201,
            "message": "Customer created successfully",
            "data": {
                "customer_name": customer.name
            }
        }

    except json.JSONDecodeError:
        frappe.response["http_status_code"] = 400
        frappe.response["message"] = {"error": "Invalid JSON format."}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Create Customer API Error")
        frappe.response["http_status_code"] = 500
        frappe.response["message"] = {"error": str(e)}




@frappe.whitelist()
@role_required(["Admin"])
def get_parent_companies():
    try:
        parent_companies = frappe.get_all(
            "Customer DB",
            fields=["company_name"]
        )

        company_list = [d["company_name"] for d in parent_companies if d["company_name"]]

        return {"data" :set(company_list)}

    except Exception as e:
        frappe.log_error(f"Error in get_parent_companies: {str(e)}")
        frappe.throw("Something went wrong while fetching parent companies.")
