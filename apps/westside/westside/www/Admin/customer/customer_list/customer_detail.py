import json
import frappe
from frappe import _
from westside.www.Authentication.auth_decorators import role_required



@frappe.whitelist()
@role_required(["Admin"])
def get_customer_details(customer_id):
    try:
        if not customer_id:
            return {
                "status": 400,
                "message": "Customer ID is required"
            }

        customer = frappe.get_doc("Customer DB", customer_id)

        customer_country_name = {}
        if customer.customer_country:
            country_code = customer.customer_country.lower()
            country_doc = frappe.db.get_value("Country", customer.customer_country, ["name", "country_name", "code"], as_dict=True)
            if country_doc:
                customer_country_name = country_doc

        customer_data = {
            "customer_id": customer.name,
            "first_name": customer.first_name,
            "last_name": customer.last_name,
            "company_name": customer.company_name,
            "parent_company": customer.parent_company,
            "phone": customer.phone,
            "contact": customer.contact,
            "email_id": customer.email_id,
            "customer_zip": customer.customer_zip,
            "customer_city": customer.customer_city,
            "customer_state": customer.customer_state,
            "customer_country": customer_country_name,
            "tax_id1": customer.tax_id1,
            "tax_id2": customer.tax_id2, 
            "tax_id3": customer.tax_id3,
            "customer_free_form_address": customer.customer_free_form_address, 
            "license_attachments" : customer.license_attachments,
            "notification_email" : customer.notification_email,
            "flag_for_custom_docs": customer.flag_for_custom_docs,
            "status": customer.status,
            "street_name": customer.street_name,
            "street_number": customer.street_number,
            "po_box": customer.po_box
        }     
        
        site_url = frappe.utils.get_url()
        license_attachments = []
        for row in customer.license_attachments:
            full_image_url = row.license if row.license else ""
            file_name = row.file_name if row.file_name else "file_name.jpg"
            license_attachments.append({"image": full_image_url, "file_name": file_name })
                     
        customer_data["license_attachments"] = license_attachments

        return {
            "status": 200,
            "message": "Customer details fetched successfully",
            "data": customer_data
        }

    except frappe.DoesNotExistError:
        return {
            "status": 404,
            "message": "Customer not found"
        }
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get Customer Detail API Error")
        return {
            "status": 500,
            "message": {"error": str(e)}
        }


 
@frappe.whitelist()
@role_required(["Admin"])
def get_customer_Booking_details(customer_id, page=1, page_size=10):
    try:
        if not customer_id:
            return {
                "status": 400,
                "message": "Customer ID is required"
            }

        try:
            page = int(page)
            page_size = int(page_size)
        except ValueError:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Invalid page or page_size value. Must be an integer."}
            return

        if page < 1 or page_size < 1:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Page and page_size must be greater than zero."}
            return

        offset = (page - 1) * page_size

        customer = frappe.get_doc("Customer DB", customer_id)

        booking_requests = frappe.get_all(
            "Booking Request",
            filters={"consignee": customer.name},
            fields=[
                "name", "contract_number", "carrier_booking_number",
                "doc_cut_of_date", "port_cut_of_date", "place_of_carrier_receipt",
                "place_of_carrier_delivery", "booking_status", "inttra_response_status"
            ],
            limit_start=offset,
            limit_page_length=page_size
        )

        total_records = frappe.db.count("Booking Request", filters={"consignee": customer.name})

        booking_data_list = []

        for booking_request_doc in booking_requests:
            full_booking_doc = frappe.get_doc("Booking Request", booking_request_doc.name)
            total_containers = sum([int(container.number_of_containers or 0) for container in full_booking_doc.containers])

            place_of_carrier_receipt = None
            if booking_request_doc.place_of_carrier_receipt:
                place_of_carrier_receipt = frappe.get_value(
                        "UNLOCODE Locations",  
                        booking_request_doc.place_of_carrier_receipt,
                        ["name", "locode", "country_code", "country", "location_name"],
                        as_dict=True
                    )
            
            if booking_request_doc.place_of_carrier_delivery:
                place_of_carrier_delivery = frappe.get_value(
                        "UNLOCODE Locations",
                        booking_request_doc.place_of_carrier_delivery,
                        ["name", "locode", "country_code", "country", "location_name"],
                        as_dict=True
                    )
            
            carrier_receipt_delivery_date = None
            
            main_carriage_doc = frappe.get_all(
                    "Booking Main Carriage",
                    filters={"parent": booking_request_doc["name"], "parenttype": "Booking Request"},
                    fields=["name", "etd", "eta"], 
                )
            if main_carriage_doc:
                carrier_receipt_delivery_date = main_carriage_doc[0]
            else:
                carrier_receipt_delivery_date = None

            booking_data_list.append({
                "booking_id": booking_request_doc.name,
                "contract_number": booking_request_doc.contract_number,
                "carrier_booking_number": booking_request_doc.carrier_booking_number,
                "filer_idscac": booking_request_doc.filer_idscac,
                "doc_cut_of_date": booking_request_doc.doc_cut_of_date,
                "port_cut_of_date": booking_request_doc.port_cut_of_date,
                "place_of_carrier_receipt": place_of_carrier_receipt,
                "place_of_carrier_delivery": place_of_carrier_delivery,
                "booking_status": booking_request_doc.booking_status,
                "inttra_response_status": booking_request_doc.inttra_response_status,
                "total_number_of_containers": total_containers,
                "carrier_receipt_delivery_date": carrier_receipt_delivery_date
            })
        
        total_count = frappe.db.count("Booking Request", filters={"consignee": customer.name})
        total_pages = (total_count + page_size - 1) // page_size

        return {
            "status": 200,
            "message": "Customer details fetched successfully",
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "data": booking_data_list
        }

    except frappe.DoesNotExistError:
        return {
            "status": 404,
            "message": "Customer not found"
        }
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get Customer Detail API Error")
        return {
            "status": 500,
            "message": {"error": str(e)}
        }
