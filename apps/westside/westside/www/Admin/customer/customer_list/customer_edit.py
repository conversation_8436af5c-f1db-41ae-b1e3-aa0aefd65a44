import json
import frappe
from frappe import _
from frappe.utils.file_manager import save_file
from westside.www.Authentication.auth_decorators import role_required



@frappe.whitelist()
@role_required(["Admin"])
def customer_edit():
    try:
        customer_id = frappe.request.args.get("customer_id")
        request_data = frappe.form_dict.get("data")
        
        license_images = frappe.request.files.getlist("license_images")
        license_urls_raw = frappe.form_dict.get("license_image_urls")

        if not request_data:
            return {'status': 400, 'message': 'Missing required data'}
            
        if not customer_id:
            return {'status': 400, 'message': 'Missing Customer Id.'}

        customer_doc = frappe.get_doc("Customer DB", customer_id)
        if not customer_doc:
            return {'status': 404, 'message': 'Customer not found'}
        
        try:
            data = json.loads(request_data)
        except json.JSONDecodeError as e:
            return {'status': 400, 'message': f'Invalid JSON format: {str(e)}'}

        # Check uniqueness of company_name before setting it
        if "company_name" in data:
            company_name = data["company_name"]
            duplicate = frappe.db.exists(
                "Customer DB",
                {
                    "company_name": company_name,
                    "name": ["!=", customer_id]
                }
            )
            if duplicate:
                return {
                    "status": 409,
                    "message": f"A customer with company name '{company_name}' already exists."
                }

        # country code is valid
        if "customer_country" in data:
            country_code = data["customer_country"] 
            country_doc = frappe.db.get_value("Country", {"code": country_code.lower()}, ["name"])
            if not country_doc:
                return {
                    "status_code": 404,
                    "message": f"Invalid country code: '{country_code.lower()}'."
                }
            customer_doc.customer_country = country_doc 

         # country code is valid
        if "customer_free_form_address" in data:
            customer_doc.customer_address = data["customer_free_form_address"]

        for field in [
            "first_name", "last_name", "customer_name", "company_name", "parent_company",
            "phone", "contact", "email_id", "customer_zip", "customer_city",
            "customer_state", "tax_id1", "tax_id2", "tax_id3", 
            "customer_free_form_address", "flag_for_custom_docs", "notification_email","street_name",
            "street_number", "po_box"
        ]:
            if field in data:
                setattr(customer_doc, field, data[field])

        site_url = frappe.utils.get_url()

        if license_images or license_urls_raw:
            customer_doc.set("license_attachments", []) 

        # Attach license images
        if license_images:
            customer_doc.set("license_attachments", [])  
            
            for file in license_images:
                try:
                    content = file.stream.read()
                    saved_file = save_file(
                        file.filename,
                        content,
                        "Customer DB",
                        customer_doc.name,
                        is_private=False
                    )

                    customer_doc.append("license_attachments", {
                        "license": site_url + saved_file.file_url,
                        "file_name": file.filename 
                    })
                except Exception as e:
                    frappe.log_error(frappe.get_traceback(), "License File Upload Error")

        # Add image URLs

        if license_urls_raw:
            try:
                license_entries = json.loads(license_urls_raw)
                for entry in license_entries:
                    if isinstance(entry, dict):
                        image_url = entry.get("image_url")
                        file_name = entry.get("file_name")
                        
                        if image_url and file_name:
                            customer_doc.append("license_attachments", {
                                "license": image_url,
                                "file_name": file_name
                            })
            except Exception as e:
                frappe.log_error(f"Error processing license attachments: {str(e)}")

        customer_doc.save(ignore_permissions=True)
        frappe.db.commit()

        return {'status': 200, 'message': 'Customer updated successfully'}

    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Customer Edit API Error")
        return {'status': 500, "message": f'Internal Server Error: {str(e)}'}
